# fase4_exploracao.py - Exploração e Análise de Impacto

import os
import json
import subprocess
import logging
import re
import time
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
import requests
from urllib.parse import urljoin, urlparse

@dataclass
class ExploracaoConfig:
    """Configurações para exploração"""
    timeout: int = 45
    max_exploits_per_vuln: int = 3
    safe_mode: bool = True  # Sempre True para evitar danos
    proof_of_concept_only: bool = True  # Apenas PoCs, sem exploração real
    
@dataclass
class ExploitInfo:
    """Informações de uma exploração"""
    vulnerability_id: str
    target_url: str
    exploit_type: str
    success: bool
    impact_level: str  # LOW, MEDIUM, HIGH, CRITICAL
    business_impact: str
    proof_of_concept: str
    evidence: Optional[str] = None
    remediation_priority: str = "Medium"
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class ImpactAnalysis:
    """Análise de impacto de negócio"""
    vulnerability_type: str
    technical_impact: str
    business_impact: str
    data_exposure_risk: str
    compliance_risk: str
    reputation_risk: str
    financial_impact: str
    cvss_score: Optional[float] = None

class ExploradorVulnerabilidades:
    """Classe principal para exploração ética de vulnerabilidades"""
    
    def __init__(self, alvo: str, diretorio_saida: str, config: ExploracaoConfig = None):
        self.alvo = alvo
        self.diretorio_saida = Path(diretorio_saida)
        self.config = config or ExploracaoConfig()
        self.vulnerabilidades = []
        self.exploits_realizados = []
        self._criar_estrutura_diretorios()
        self.logger = self._configurar_logging()
        self._carregar_vulnerabilidades()
    
    def _configurar_logging(self) -> logging.Logger:
        """Configura logging detalhado"""
        logger = logging.getLogger(f"exploit_{self.alvo}")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            file_handler = logging.FileHandler(
                self.diretorio_saida / "fase4_exploracao.log", 
                encoding='utf-8'
            )
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            stream_handler = logging.StreamHandler()
            stream_formatter = logging.Formatter('[Fase 4 - %(levelname)s] %(message)s')
            stream_handler.setFormatter(stream_formatter)
            logger.addHandler(stream_handler)
        return logger
    
    def _criar_estrutura_diretorios(self):
        """Cria estrutura de diretórios para a Fase 4"""
        subdirs = [
            "exploits", "proofs_of_concept", "impact_analysis", 
            "business_reports", "relatorios_fase4"
        ]
        for subdir in subdirs:
            (self.diretorio_saida / subdir).mkdir(exist_ok=True, parents=True)
    
    def _carregar_vulnerabilidades(self):
        """Carrega vulnerabilidades da Fase 3"""
        arquivo_vulns = None
        
        # Procura pelo arquivo mais recente da Fase 3
        relatorios_dir = self.diretorio_saida / "relatorios_fase3"
        if relatorios_dir.exists():
            arquivos = list(relatorios_dir.glob("vulnerabilidades_*.json"))
            if arquivos:
                arquivo_vulns = max(arquivos, key=os.path.getctime)
        
        if arquivo_vulns and arquivo_vulns.exists():
            try:
                with open(arquivo_vulns, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.vulnerabilidades = data.get('vulnerabilidades', [])
                self.logger.info(f"Carregadas {len(self.vulnerabilidades)} vulnerabilidades da Fase 3")
            except Exception as e:
                self.logger.error(f"Erro ao carregar vulnerabilidades: {e}")
                self.vulnerabilidades = []
        else:
            self.logger.warning("Nenhuma vulnerabilidade encontrada da Fase 3")
            self.vulnerabilidades = []

    def simulacao_sql_injection(self, vuln: Dict) -> Optional[ExploitInfo]:
        """Simula exploração de SQL Injection de forma segura"""
        if vuln.get('vulnerability_type') != 'SQL Injection':
            return None
            
        self.logger.info(f"🔍 Simulando SQL Injection em: {vuln.get('target_url')}")
        
        # Análise de impacto baseada na URL e contexto
        impact_analysis = self._analisar_impacto_sqli(vuln.get('target_url', ''))
        
        exploit = ExploitInfo(
            vulnerability_id=f"sqli_{hash(vuln.get('target_url', '')) % 10000}",
            target_url=vuln.get('target_url', ''),
            exploit_type="SQL Injection Simulation",
            success=True,  # Assumimos sucesso para demonstração
            impact_level=impact_analysis['impact_level'],
            business_impact=impact_analysis['business_impact'],
            proof_of_concept=self._gerar_poc_sqli(vuln.get('target_url', '')),
            evidence="Simulação controlada - sem execução real",
            remediation_priority=impact_analysis['priority']
        )
        
        return exploit

    def _analisar_impacto_sqli(self, url: str) -> Dict:
        """Analisa o impacto potencial de uma SQL Injection"""
        # Análise baseada em padrões da URL
        if any(keyword in url.lower() for keyword in ['admin', 'login', 'user', 'account']):
            return {
                'impact_level': 'CRITICAL',
                'business_impact': 'Acesso não autorizado a dados de usuários e credenciais',
                'priority': 'High'
            }
        elif any(keyword in url.lower() for keyword in ['product', 'item', 'search']):
            return {
                'impact_level': 'HIGH',
                'business_impact': 'Exposição de dados de produtos e informações comerciais',
                'priority': 'Medium'
            }
        else:
            return {
                'impact_level': 'MEDIUM',
                'business_impact': 'Possível exposição de dados da aplicação',
                'priority': 'Medium'
            }

    def _gerar_poc_sqli(self, url: str) -> str:
        """Gera Proof of Concept para SQL Injection"""
        return f"""
PROOF OF CONCEPT - SQL INJECTION
================================
URL Alvo: {url}
Payload Exemplo: ' OR '1'='1' --
Impacto: Um atacante poderia usar esta vulnerabilidade para:
1. Extrair dados sensíveis do banco de dados
2. Modificar ou deletar informações
3. Potencialmente obter acesso administrativo

RECOMENDAÇÃO URGENTE:
- Implementar prepared statements
- Validar e sanitizar todas as entradas
- Aplicar princípio do menor privilégio no banco
"""

    def simulacao_xss(self, vuln: Dict) -> Optional[ExploitInfo]:
        """Simula exploração de XSS de forma segura"""
        if 'XSS' not in vuln.get('vulnerability_type', ''):
            return None
            
        self.logger.info(f"🔍 Simulando XSS em: {vuln.get('target_url')}")
        
        impact_analysis = self._analisar_impacto_xss(vuln.get('target_url', ''))
        
        exploit = ExploitInfo(
            vulnerability_id=f"xss_{hash(vuln.get('target_url', '')) % 10000}",
            target_url=vuln.get('target_url', ''),
            exploit_type="XSS Simulation",
            success=True,
            impact_level=impact_analysis['impact_level'],
            business_impact=impact_analysis['business_impact'],
            proof_of_concept=self._gerar_poc_xss(vuln.get('target_url', '')),
            evidence="Simulação controlada - sem execução real",
            remediation_priority=impact_analysis['priority']
        )
        
        return exploit

    def _analisar_impacto_xss(self, url: str) -> Dict:
        """Analisa o impacto potencial de XSS"""
        if any(keyword in url.lower() for keyword in ['admin', 'dashboard', 'panel']):
            return {
                'impact_level': 'HIGH',
                'business_impact': 'Roubo de sessões administrativas e controle da aplicação',
                'priority': 'High'
            }
        else:
            return {
                'impact_level': 'MEDIUM',
                'business_impact': 'Roubo de cookies de usuários e defacement',
                'priority': 'Medium'
            }

    def _gerar_poc_xss(self, url: str) -> str:
        """Gera Proof of Concept para XSS"""
        return f"""
PROOF OF CONCEPT - CROSS-SITE SCRIPTING (XSS)
==============================================
URL Alvo: {url}
Payload Exemplo: <script>alert('XSS')</script>
Impacto: Um atacante poderia usar esta vulnerabilidade para:
1. Roubar cookies e sessões de usuários
2. Redirecionar usuários para sites maliciosos
3. Executar ações em nome do usuário
4. Realizar defacement da página

RECOMENDAÇÃO URGENTE:
- Implementar Content Security Policy (CSP)
- Sanitizar todas as saídas HTML
- Validar e filtrar entradas do usuário
"""

    def analise_componentes_vulneraveis(self, vuln: Dict) -> Optional[ExploitInfo]:
        """Analisa componentes com vulnerabilidades conhecidas"""
        if vuln.get('discovered_by') != 'Nuclei':
            return None
            
        self.logger.info(f"🔍 Analisando componente vulnerável: {vuln.get('vulnerability_type')}")
        
        # Determina severidade baseada no tipo de vulnerabilidade
        severity = vuln.get('severity', 'Medium').upper()
        
        if severity in ['CRITICAL', 'HIGH']:
            impact_level = 'HIGH'
            business_impact = 'Componente com vulnerabilidade conhecida pode ser explorado remotamente'
            priority = 'High'
        else:
            impact_level = 'MEDIUM'
            business_impact = 'Componente desatualizado com possíveis riscos de segurança'
            priority = 'Medium'
        
        exploit = ExploitInfo(
            vulnerability_id=f"comp_{hash(vuln.get('target_url', '')) % 10000}",
            target_url=vuln.get('target_url', ''),
            exploit_type="Component Vulnerability",
            success=True,
            impact_level=impact_level,
            business_impact=business_impact,
            proof_of_concept=self._gerar_poc_componente(vuln),
            evidence=f"Componente identificado: {vuln.get('vulnerability_type')}",
            remediation_priority=priority
        )
        
        return exploit

    def _gerar_poc_componente(self, vuln: Dict) -> str:
        """Gera PoC para vulnerabilidade de componente"""
        return f"""
PROOF OF CONCEPT - COMPONENTE VULNERÁVEL
========================================
Componente: {vuln.get('vulnerability_type', 'Unknown')}
URL: {vuln.get('target_url', '')}
Severidade: {vuln.get('severity', 'Unknown')}

DESCRIÇÃO:
{vuln.get('description', 'Componente com vulnerabilidade conhecida detectado')}

IMPACTO:
Este componente possui vulnerabilidades conhecidas que podem ser exploradas
por atacantes para comprometer a segurança da aplicação.

RECOMENDAÇÃO URGENTE:
- Atualizar o componente para a versão mais recente
- Verificar se há patches de segurança disponíveis
- Considerar alternativas mais seguras se o componente não for mantido
"""

    def encadeamento_vulnerabilidades(self) -> List[ExploitInfo]:
        """Analisa possível encadeamento de vulnerabilidades"""
        self.logger.info("🔗 Analisando encadeamento de vulnerabilidades...")
        
        encadeamentos = []
        
        # Verifica se há múltiplas vulnerabilidades no mesmo domínio
        dominios = {}
        for vuln in self.vulnerabilidades:
            url = vuln.get('target_url', '')
            dominio = urlparse(url).netloc
            if dominio not in dominios:
                dominios[dominio] = []
            dominios[dominio].append(vuln)
        
        # Analisa domínios com múltiplas vulnerabilidades
        for dominio, vulns in dominios.items():
            if len(vulns) > 1:
                exploit = ExploitInfo(
                    vulnerability_id=f"chain_{hash(dominio) % 10000}",
                    target_url=f"https://{dominio}",
                    exploit_type="Vulnerability Chaining",
                    success=True,
                    impact_level="HIGH",
                    business_impact=f"Múltiplas vulnerabilidades ({len(vulns)}) no mesmo domínio permitem ataques encadeados",
                    proof_of_concept=self._gerar_poc_encadeamento(vulns),
                    evidence=f"{len(vulns)} vulnerabilidades identificadas no domínio {dominio}",
                    remediation_priority="High"
                )
                encadeamentos.append(exploit)
        
        return encadeamentos

    def _gerar_poc_encadeamento(self, vulns: List[Dict]) -> str:
        """Gera PoC para encadeamento de vulnerabilidades"""
        tipos = [v.get('vulnerability_type', 'Unknown') for v in vulns]
        return f"""
PROOF OF CONCEPT - ENCADEAMENTO DE VULNERABILIDADES
===================================================
Vulnerabilidades Identificadas: {', '.join(tipos)}
Total: {len(vulns)} vulnerabilidades

CENÁRIO DE ATAQUE:
Um atacante poderia combinar essas vulnerabilidades para:
1. Obter acesso inicial através de uma vulnerabilidade
2. Escalar privilégios usando outras falhas
3. Manter persistência no sistema
4. Exfiltrar dados sensíveis

IMPACTO CRÍTICO:
O encadeamento de vulnerabilidades aumenta significativamente
o risco e o impacto potencial de um ataque bem-sucedido.

RECOMENDAÇÃO URGENTE:
- Priorizar correção de todas as vulnerabilidades identificadas
- Implementar defesa em profundidade
- Monitoramento contínuo de segurança
"""

    def executar_fase_completa(self) -> Dict:
        """Executa o fluxo completo da Fase 4"""
        inicio = datetime.now()
        try:
            self.logger.info(f"🚀 INICIANDO FASE 4: EXPLORAÇÃO E ANÁLISE DE IMPACTO PARA {self.alvo}")
            self.logger.info(f"🎯 Vulnerabilidades para análise: {len(self.vulnerabilidades)}")
            
            if not self.vulnerabilidades:
                self.logger.warning("⚠️ Nenhuma vulnerabilidade encontrada para exploração")
                return {'sucesso': False, 'erro': 'Nenhuma vulnerabilidade para analisar'}
            
            # Executa simulações de exploração
            for vuln in self.vulnerabilidades:
                # SQL Injection
                exploit_sqli = self.simulacao_sql_injection(vuln)
                if exploit_sqli:
                    self.exploits_realizados.append(exploit_sqli)
                
                # XSS
                exploit_xss = self.simulacao_xss(vuln)
                if exploit_xss:
                    self.exploits_realizados.append(exploit_xss)
                
                # Componentes vulneráveis
                exploit_comp = self.analise_componentes_vulneraveis(vuln)
                if exploit_comp:
                    self.exploits_realizados.append(exploit_comp)
            
            # Análise de encadeamento
            encadeamentos = self.encadeamento_vulnerabilidades()
            self.exploits_realizados.extend(encadeamentos)
            
            # Gera relatório
            self._gerar_relatorio_fase4()
            
            duracao = datetime.now() - inicio
            self.logger.info("="*60)
            self.logger.info("🎉 FASE 4 CONCLUÍDA COM SUCESSO!")
            self.logger.info(f"💥 Total de explorações simuladas: {len(self.exploits_realizados)}")
            self.logger.info(f"⏱️  Duração total: {duracao}")
            
            return {
                'sucesso': True,
                'exploits_realizados': len(self.exploits_realizados),
                'duracao': str(duracao)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erro crítico na Fase 4: {str(e)}", exc_info=True)
            return {'sucesso': False, 'erro': str(e)}

    def _gerar_relatorio_fase4(self):
        """Gera relatório detalhado da Fase 4"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Análise de impacto por severidade
        impacto_por_nivel = {}
        for exploit in self.exploits_realizados:
            nivel = exploit.impact_level
            impacto_por_nivel[nivel] = impacto_por_nivel.get(nivel, 0) + 1
        
        # Relatório JSON
        relatorio_json = {
            "alvo": self.alvo,
            "timestamp": timestamp,
            "total_exploits": len(self.exploits_realizados),
            "impacto_por_nivel": impacto_por_nivel,
            "exploits": [asdict(e) for e in self.exploits_realizados],
            "resumo_executivo": self._gerar_resumo_executivo()
        }
        
        arquivo_json = self.diretorio_saida / "relatorios_fase4" / f"exploracao_{timestamp}.json"
        with open(arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(relatorio_json, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📋 Relatório de exploração salvo: {arquivo_json}")

    def _gerar_resumo_executivo(self) -> Dict:
        """Gera resumo executivo dos riscos"""
        criticos = len([e for e in self.exploits_realizados if e.impact_level == 'CRITICAL'])
        altos = len([e for e in self.exploits_realizados if e.impact_level == 'HIGH'])
        medios = len([e for e in self.exploits_realizados if e.impact_level == 'MEDIUM'])
        
        return {
            "riscos_criticos": criticos,
            "riscos_altos": altos,
            "riscos_medios": medios,
            "recomendacao_geral": "Correção imediata das vulnerabilidades críticas e altas é essencial",
            "impacto_negocio": "Múltiplas vulnerabilidades podem comprometer a segurança e reputação"
        }

def run(alvo: str, diretorio_saida: str, config_dict: Dict = None):
    """Interface principal para executar a Fase 4"""
    config = ExploracaoConfig(**config_dict) if config_dict else ExploracaoConfig()
    explorador = ExploradorVulnerabilidades(alvo, diretorio_saida, config)
    return explorador.executar_fase_completa()
