---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug report
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
1. Run '...'
2. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Running environment:**
 - sqlmap version [e.g. ********#dev]
 - Installation method [e.g. pip]
 - Operating system: [e.g. Microsoft Windows 11]
 - Python version [e.g. 3.11.2]

**Target details:**
 - DBMS [e.g. Microsoft SQL Server]
 - SQLi techniques found by sqlmap [e.g. error-based and boolean-based blind]
 - WAF/IPS [if any]
 - Relevant console output [if any]
 - Exception traceback [if any]

**Additional context**
Add any other context about the problem here.
