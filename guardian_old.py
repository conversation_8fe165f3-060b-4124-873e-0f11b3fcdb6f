# guardian.py - Guardian IA - Sistema Completo de 6 Fases

import os
import time
from datetime import datetime
import fase1_recon
import fase2_enum
import fase3_vulnerabilidades
import fase4_exploracao
import fase5_pos_exploracao
import fase6_relatorio_inteligente

def main():
    """
    Guardian IA - Sistema Completo de Pentest Automatizado

    Executa as 6 fases da metodologia Guardian IA:
    1. Reconhecimento Estratégico e Coleta de Inteligência
    2. Enumeração Ativa e Mapeamento da Superfície de Ataque
    3. Análise de Vulnerabilidades e Testes de Intrusão
    4. Exploração e Análise de Impacto
    5. Pós-Exploração e Análise de Movimento Lateral
    6. Relatório Inteligente e Plano de Ação
    """

    # --- Configuração Inicial ---
    alvo = "hackerone.com"  # Alvo principal
    diretorio_base_projeto = os.path.abspath(os.path.dirname(__file__))
    diretorio_saida_alvo = os.path.join(diretorio_base_projeto, "resultados", alvo)

    print("="*80)
    print("🛡️  GUARDIAN IA - SISTEMA DE PENTEST AUTOMATIZADO")
    print("   'Protegendo o invisível. Antecipando o inevitável.'")
    print("="*80)
    print(f"🎯 ALVO: {alvo}")
    print(f"📁 DIRETÓRIO DE SAÍDA: {diretorio_saida_alvo}")
    print(f"⏰ INÍCIO: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("="*80)

    inicio_execucao = datetime.now()
    resultados_fases = {}

    try:
        # --- FASE 1: RECONHECIMENTO ESTRATÉGICO ---
        print("\n🔍 FASE 1: RECONHECIMENTO ESTRATÉGICO E COLETA DE INTELIGÊNCIA")
        print("-" * 60)
        config_fase1 = {
            "timeout": 60,
            "max_subdominios": 1000,
            "verificar_httpx": True,
            "incluir_tecnologias": True
        }
        resultado_fase1 = fase1_recon.run(alvo, diretorio_saida_alvo, config_fase1)
        resultados_fases['fase1'] = resultado_fase1
        print(f"✅ Fase 1 concluída: {resultado_fase1}")

        # --- FASE 2: ENUMERAÇÃO ATIVA ---
        print("\n🎯 FASE 2: ENUMERAÇÃO ATIVA E MAPEAMENTO DA SUPERFÍCIE DE ATAQUE")
        print("-" * 60)
        config_fase2 = {
            "timeout": 45,
            "max_diretorios": 500,
            "incluir_parametros": True,
            "wordlist_personalizada": True
        }
        resultado_fase2 = fase2_enum.run(alvo, diretorio_saida_alvo, config_fase2)
        resultados_fases['fase2'] = resultado_fase2
        print(f"✅ Fase 2 concluída: {resultado_fase2}")

        # --- FASE 3: ANÁLISE DE VULNERABILIDADES ---
        print("\n🔬 FASE 3: ANÁLISE DE VULNERABILIDADES E TESTES DE INTRUSÃO")
        print("-" * 60)
        config_fase3 = {
            "timeout": 60,
            "sqlmap_risk": 1,
            "safe_mode": True,
            "incluir_nuclei": True
        }
        resultado_fase3 = fase3_vulnerabilidades.run(alvo, diretorio_saida_alvo, config_fase3)
        resultados_fases['fase3'] = resultado_fase3
        print(f"✅ Fase 3 concluída: {resultado_fase3}")

        # --- FASE 4: EXPLORAÇÃO E ANÁLISE DE IMPACTO ---
        print("\n💥 FASE 4: EXPLORAÇÃO E ANÁLISE DE IMPACTO")
        print("-" * 60)
        config_fase4 = {
            "timeout": 45,
            "max_exploits_per_vuln": 3,
            "safe_mode": True,
            "proof_of_concept_only": True
        }
        resultado_fase4 = fase4_exploracao.run(alvo, diretorio_saida_alvo, config_fase4)
        resultados_fases['fase4'] = resultado_fase4
        print(f"✅ Fase 4 concluída: {resultado_fase4}")

        # --- FASE 5: PÓS-EXPLORAÇÃO E MOVIMENTO LATERAL ---
        print("\n🔄 FASE 5: PÓS-EXPLORAÇÃO E ANÁLISE DE MOVIMENTO LATERAL")
        print("-" * 60)
        config_fase5 = {
            "analise_profunda": True,
            "incluir_cenarios_hipoteticos": True,
            "avaliar_movimento_lateral": True
        }
        resultado_fase5 = fase5_pos_exploracao.run(alvo, diretorio_saida_alvo, config_fase5)
        resultados_fases['fase5'] = resultado_fase5
        print(f"✅ Fase 5 concluída: {resultado_fase5}")

        # --- FASE 6: RELATÓRIO INTELIGENTE ---
        print("\n📊 FASE 6: RELATÓRIO INTELIGENTE E PLANO DE AÇÃO")
        print("-" * 60)
        config_fase6 = {
            "incluir_graficos": True,
            "formato_saida": ["json", "html"],
            "nivel_detalhamento": "completo",
            "incluir_recomendacoes": True
        }
        resultado_fase6 = fase6_relatorio_inteligente.run(alvo, diretorio_saida_alvo, config_fase6)
        resultados_fases['fase6'] = resultado_fase6
        print(f"✅ Fase 6 concluída: {resultado_fase6}")

        # --- RESUMO FINAL ---
        duracao_total = datetime.now() - inicio_execucao
        print("\n" + "="*80)
        print("🎉 GUARDIAN IA CONCLUÍDO COM SUCESSO!")
        print("="*80)
        print(f"⏱️  Duração Total: {duracao_total}")
        print(f"📁 Resultados salvos em: {diretorio_saida_alvo}")

        # Exibe resumo de cada fase
        for fase, resultado in resultados_fases.items():
            if isinstance(resultado, dict) and resultado.get('sucesso'):
                print(f"✅ {fase.upper()}: Sucesso")
            else:
                print(f"⚠️  {fase.upper()}: {resultado}")

        # Informações do relatório final
        if resultado_fase6.get('sucesso'):
            print(f"\n📊 Score de Segurança: {resultado_fase6.get('score_seguranca', 'N/A')}/100")
            print(f"🎯 Nível de Risco: {resultado_fase6.get('nivel_risco', 'N/A')}")
            print(f"🔍 Vulnerabilidades Encontradas: {resultado_fase6.get('vulnerabilidades_consolidadas', 'N/A')}")

        print("\n🛡️  Guardian IA - Protegendo o invisível. Antecipando o inevitável.")
        print("="*80)

    except KeyboardInterrupt:
        print("\n⚠️  Execução interrompida pelo usuário")
        print("Os resultados parciais foram salvos no diretório de saída")
    except Exception as e:
        print(f"\n❌ Erro crítico durante a execução: {str(e)}")
        print("Verifique os logs para mais detalhes")
        return False

    return True

if __name__ == "__main__":
    main()
