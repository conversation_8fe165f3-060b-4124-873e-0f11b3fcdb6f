# GUARDIAN IA - Instalacao Simples de Ferramentas
# Execute como Administrador

Write-Host "GUARDIAN IA - Instalacao de Ferramentas de Pentest" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Funcao para verificar se comando existe
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Instala Chocolatey se nao existir
if (-not (Test-Command "choco")) {
    Write-Host "Instalando Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Instala Go se nao existir
if (-not (Test-Command "go")) {
    Write-Host "Instalando Go..." -ForegroundColor Yellow
    choco install golang -y
    refreshenv
}

# Instala Python se nao existir
if (-not (Test-Command "pip")) {
    Write-Host "Instalando Python..." -ForegroundColor Yellow
    choco install python -y
    refreshenv
}

Write-Host "Instalando ferramentas Go..." -ForegroundColor Green

# Lista de ferramentas Go
$goTools = @(
    "github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest",
    "github.com/owasp-amass/amass/v4/...@master",
    "github.com/tomnomnom/assetfinder@latest",
    "github.com/projectdiscovery/httpx/cmd/httpx@latest",
    "github.com/OJ/gobuster/v3@latest",
    "github.com/sensepost/gowitness@latest",
    "github.com/projectdiscovery/katana/cmd/katana@latest",
    "github.com/jaeles-project/gospider@latest",
    "github.com/hakluke/hakrawler@latest",
    "github.com/tomnomnom/gf@latest",
    "github.com/hahwul/dalfox/v2@latest",
    "github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest"
)

foreach ($tool in $goTools) {
    Write-Host "Instalando $tool..." -ForegroundColor Yellow
    go install $tool
}

Write-Host "Instalando ferramentas Python..." -ForegroundColor Green

# Lista de ferramentas Python
$pythonTools = @(
    "sqlmap",
    "paramspider",
    "arjun"
)

foreach ($tool in $pythonTools) {
    Write-Host "Instalando $tool..." -ForegroundColor Yellow
    pip install $tool
}

Write-Host "Instalando ferramentas via Chocolatey..." -ForegroundColor Green

# Lista de ferramentas Chocolatey
$chocoTools = @(
    "findomain",
    "feroxbuster"
)

foreach ($tool in $chocoTools) {
    Write-Host "Instalando $tool..." -ForegroundColor Yellow
    choco install $tool -y
}

Write-Host "Atualizando templates do Nuclei..." -ForegroundColor Yellow
nuclei -update-templates

Write-Host "Verificacao final:" -ForegroundColor Cyan

# Lista de todas as ferramentas para verificar
$allTools = @(
    "subfinder", "amass", "assetfinder", "httpx", "gobuster", "gowitness",
    "katana", "gospider", "hakrawler", "gf", "dalfox", "nuclei",
    "sqlmap", "paramspider", "arjun", "findomain", "feroxbuster"
)

$installedCount = 0
foreach ($tool in $allTools) {
    if (Test-Command $tool) {
        Write-Host "OK: $tool" -ForegroundColor Green
        $installedCount++
    } else {
        Write-Host "FALTANDO: $tool" -ForegroundColor Red
    }
}

Write-Host "Resultado: $installedCount/$($allTools.Count) ferramentas instaladas" -ForegroundColor Cyan

if ($installedCount -eq $allTools.Count) {
    Write-Host "SUCESSO! Todas as ferramentas foram instaladas!" -ForegroundColor Green
} else {
    Write-Host "AVISO: Algumas ferramentas nao foram instaladas." -ForegroundColor Yellow
}

Write-Host "Para testar: python guardian_main.py --target exemplo.com" -ForegroundColor White
pause
