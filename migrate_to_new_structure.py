#!/usr/bin/env python3
# migrate_to_new_structure.py - Script para migrar arquivos para nova estrutura

import os
import shutil
from pathlib import Path

def print_banner():
    """Exibe banner de migração"""
    print("="*80)
    print("🔄 GUARDIAN IA - MIGRAÇÃO PARA NOVA ESTRUTURA")
    print("   Organizando arquivos em estrutura profissional")
    print("="*80)

def backup_old_files():
    """Cria backup dos arquivos antigos"""
    print("\n📦 Criando backup dos arquivos antigos...")
    
    backup_dir = Path("backup_old_structure")
    backup_dir.mkdir(exist_ok=True)
    
    old_files = [
        "guardian.py",
        "fase1_recon.py", 
        "fase2_enum.py",
        "fase3_vulnerabilidades.py",
        "fase4_exploracao.py",
        "fase5_pos_exploracao.py",
        "fase6_relatorio_inteligente.py"
    ]
    
    backed_up = 0
    for file in old_files:
        if Path(file).exists():
            shutil.copy2(file, backup_dir / file)
            print(f"✅ Backup criado: {file}")
            backed_up += 1
    
    if backed_up > 0:
        print(f"📦 {backed_up} arquivos salvos em backup_old_structure/")
    else:
        print("ℹ️  Nenhum arquivo antigo encontrado para backup")
    
    return backed_up > 0

def create_new_structure():
    """Cria nova estrutura de diretórios"""
    print("\n📁 Criando nova estrutura de diretórios...")
    
    directories = [
        "src",
        "src/core", 
        "src/phases",
        "src/utils",
        "config",
        "results",
        "logs",
        "wordlists"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True, parents=True)
        print(f"✅ Diretório criado: {directory}")

def migrate_files():
    """Migra arquivos para nova estrutura"""
    print("\n🔄 Migrando arquivos para nova estrutura...")
    
    # Mapeamento de arquivos antigos para novos
    file_mapping = {
        "fase1_recon.py": "src/phases/fase1_recon.py",
        "fase2_enum.py": "src/phases/fase2_enum.py", 
        "fase3_vulnerabilidades.py": "src/phases/fase3_vulnerabilidades.py",
        "fase4_exploracao.py": "src/phases/fase4_exploracao.py",
        "fase5_pos_exploracao.py": "src/phases/fase5_pos_exploracao.py",
        "fase6_relatorio_inteligente.py": "src/phases/fase6_relatorio.py"
    }
    
    migrated = 0
    for old_file, new_file in file_mapping.items():
        if Path(old_file).exists():
            # Cria diretório de destino se não existir
            Path(new_file).parent.mkdir(exist_ok=True, parents=True)
            
            # Move arquivo
            shutil.move(old_file, new_file)
            print(f"✅ Migrado: {old_file} → {new_file}")
            migrated += 1
        else:
            print(f"⚠️  Arquivo não encontrado: {old_file}")
    
    return migrated

def update_imports_in_files():
    """Atualiza imports nos arquivos migrados"""
    print("\n🔧 Atualizando imports nos arquivos migrados...")
    
    phase_files = [
        "src/phases/fase1_recon.py",
        "src/phases/fase2_enum.py",
        "src/phases/fase3_vulnerabilidades.py", 
        "src/phases/fase4_exploracao.py",
        "src/phases/fase5_pos_exploracao.py",
        "src/phases/fase6_relatorio.py"
    ]
    
    updated = 0
    for file_path in phase_files:
        if Path(file_path).exists():
            try:
                # Lê conteúdo do arquivo
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Atualiza imports básicos
                content = content.replace(
                    "from pathlib import Path",
                    "from pathlib import Path\nfrom ..core.logger import get_logger\nfrom ..core.config import DEFAULT_CONFIG"
                )
                
                # Adiciona imports do core se não existirem
                if "from ..core" not in content:
                    # Adiciona imports no início do arquivo após os imports padrão
                    lines = content.split('\n')
                    import_end = 0
                    
                    for i, line in enumerate(lines):
                        if line.startswith('import ') or line.startswith('from '):
                            import_end = i + 1
                    
                    # Insere novos imports
                    new_imports = [
                        "from ..core.logger import get_logger",
                        "from ..core.config import DEFAULT_CONFIG",
                        "from ..utils.file_utils import save_json, load_json",
                        "from ..utils.command_utils import CommandExecutor"
                    ]
                    
                    for import_line in reversed(new_imports):
                        lines.insert(import_end, import_line)
                    
                    content = '\n'.join(lines)
                
                # Salva arquivo atualizado
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ Imports atualizados: {file_path}")
                updated += 1
                
            except Exception as e:
                print(f"❌ Erro ao atualizar {file_path}: {e}")
    
    return updated

def clean_old_files():
    """Remove arquivos antigos após migração"""
    print("\n🧹 Limpando arquivos antigos...")
    
    old_files = [
        "guardian.py",  # Será substituído por guardian_main.py
        "resultados",   # Diretório antigo
    ]
    
    cleaned = 0
    for item in old_files:
        path = Path(item)
        if path.exists():
            if path.is_file():
                path.unlink()
                print(f"✅ Arquivo removido: {item}")
            elif path.is_dir():
                # Move diretório antigo para novo local
                if item == "resultados" and not Path("results").exists():
                    shutil.move(item, "results")
                    print(f"✅ Diretório migrado: {item} → results")
                else:
                    print(f"ℹ️  Diretório mantido: {item}")
            cleaned += 1
    
    return cleaned

def verify_migration():
    """Verifica se a migração foi bem-sucedida"""
    print("\n🔍 Verificando migração...")
    
    required_files = [
        "guardian_main.py",
        "src/__init__.py",
        "src/core/guardian.py",
        "src/core/config.py",
        "src/core/logger.py",
        "src/utils/file_utils.py",
        "src/utils/command_utils.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Arquivos em falta:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ Todos os arquivos necessários estão presentes!")
        return True

def main():
    """Função principal de migração"""
    print_banner()
    
    # Verifica se já está na nova estrutura
    if Path("src/core/guardian.py").exists():
        print("ℹ️  Nova estrutura já existe. Migração não necessária.")
        return
    
    try:
        # Executa migração
        backup_created = backup_old_files()
        create_new_structure()
        migrated_files = migrate_files()
        updated_imports = update_imports_in_files()
        cleaned_files = clean_old_files()
        
        # Verifica migração
        success = verify_migration()
        
        print("\n" + "="*80)
        if success:
            print("🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!")
            print("="*80)
            print(f"📦 Backup criado: {'Sim' if backup_created else 'Não necessário'}")
            print(f"🔄 Arquivos migrados: {migrated_files}")
            print(f"🔧 Imports atualizados: {updated_imports}")
            print(f"🧹 Arquivos limpos: {cleaned_files}")
            print("\nPara executar o Guardian IA:")
            print("python guardian_main.py --target exemplo.com")
        else:
            print("❌ MIGRAÇÃO INCOMPLETA!")
            print("Verifique os arquivos em falta e execute novamente.")
        print("="*80)
        
    except Exception as e:
        print(f"\n❌ Erro durante migração: {e}")
        print("Verifique os backups em backup_old_structure/")

if __name__ == "__main__":
    main()
