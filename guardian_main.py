#!/usr/bin/env python3
# guardian_main.py - Ponto de entrada principal do Guardian IA

"""
Guardian IA - Sistema de Pentest Automatizado
"Protegendo o invisível. Antecipando o inevitável."

Sistema completo de penetration testing automatizado seguindo metodologia de 6 fases:
1. Reconhecimento Estratégico e Coleta de Inteligência
2. Enumeração Ativa e Mapeamento da Superfície de Ataque
3. Análise de Vulnerabilidades e Testes de Intrusão
4. Exploração e Análise de Impacto
5. Pós-Exploração e Análise de Movimento Lateral
6. Relatório Inteligente e Plano de Ação

Uso:
    python guardian_main.py --target exemplo.com
    python guardian_main.py --target exemplo.com --config config.json
    python guardian_main.py --target exemplo.com --phases 1,2,3
"""

import sys
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional

# Adiciona src ao path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.guardian import GuardianIA
from src.core.config import (
    DEFAULT_CONFIG, DEFAULT_RECON_CONFIG, DEFAULT_ENUM_CONFIG,
    DEFAULT_VULN_CONFIG, DEFAULT_EXPLORACAO_CONFIG, 
    DEFAULT_POS_EXPLORACAO_CONFIG, DEFAULT_RELATORIO_CONFIG
)
from src.core.logger import get_logger

def parse_arguments():
    """Parse argumentos da linha de comando"""
    parser = argparse.ArgumentParser(
        description="Guardian IA - Sistema de Pentest Automatizado",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  %(prog)s --target exemplo.com
  %(prog)s --target exemplo.com --safe-mode
  %(prog)s --target exemplo.com --phases 1,2,3
  %(prog)s --target exemplo.com --config custom_config.json
  %(prog)s --target exemplo.com --output-dir ./custom_results
        """
    )
    
    # Argumentos obrigatórios
    parser.add_argument(
        "--target", "-t",
        required=True,
        help="Domínio alvo para análise (ex: exemplo.com)"
    )
    
    # Argumentos opcionais
    parser.add_argument(
        "--phases", "-p",
        help="Fases para executar (ex: 1,2,3 ou 1-3). Padrão: todas"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=Path,
        help="Arquivo de configuração personalizada (JSON)"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        type=Path,
        help="Diretório de saída personalizado"
    )
    
    parser.add_argument(
        "--safe-mode",
        action="store_true",
        help="Força modo seguro (apenas PoCs, sem exploração real)"
    )
    
    parser.add_argument(
        "--aggressive",
        action="store_true",
        help="Modo agressivo (apenas em ambientes controlados)"
    )
    
    parser.add_argument(
        "--timeout",
        type=int,
        default=60,
        help="Timeout padrão para ferramentas (segundos). Padrão: 60"
    )
    
    parser.add_argument(
        "--threads",
        type=int,
        default=30,
        help="Número de threads para ferramentas. Padrão: 30"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Saída verbosa (nível DEBUG)"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Saída mínima (apenas erros)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Guardian IA v1.0.0"
    )
    
    return parser.parse_args()

def load_custom_config(config_file: Path) -> Optional[Dict]:
    """Carrega configuração personalizada"""
    try:
        if not config_file.exists():
            print(f"❌ Arquivo de configuração não encontrado: {config_file}")
            return None
        
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar configuração: {e}")
        return None

def parse_phases(phases_str: str) -> List[int]:
    """Parse string de fases para lista de números"""
    phases = []
    
    for part in phases_str.split(','):
        part = part.strip()
        
        if '-' in part:
            # Range de fases (ex: 1-3)
            start, end = map(int, part.split('-'))
            phases.extend(range(start, end + 1))
        else:
            # Fase individual
            phases.append(int(part))
    
    # Remove duplicatas e ordena
    return sorted(list(set(phases)))

def build_config_from_args(args) -> Dict:
    """Constrói configuração baseada nos argumentos"""
    config = {}
    
    # Configuração global
    global_config = DEFAULT_CONFIG.__dict__.copy()
    
    if args.safe_mode:
        global_config["safe_mode"] = True
    
    if args.aggressive:
        global_config["safe_mode"] = False
    
    if args.verbose:
        global_config["log_level"] = "DEBUG"
    elif args.quiet:
        global_config["log_level"] = "ERROR"
    
    config["global"] = global_config
    
    # Configurações por fase com argumentos
    recon_config = DEFAULT_RECON_CONFIG.__dict__.copy()
    recon_config["timeout"] = args.timeout
    recon_config["threads"] = args.threads
    config["recon"] = recon_config
    
    enum_config = DEFAULT_ENUM_CONFIG.__dict__.copy()
    enum_config["timeout"] = args.timeout
    enum_config["threads"] = args.threads
    config["enum"] = enum_config
    
    vuln_config = DEFAULT_VULN_CONFIG.__dict__.copy()
    vuln_config["timeout"] = args.timeout
    vuln_config["safe_mode"] = args.safe_mode or not args.aggressive
    config["vuln"] = vuln_config
    
    exploracao_config = DEFAULT_EXPLORACAO_CONFIG.__dict__.copy()
    exploracao_config["timeout"] = args.timeout
    exploracao_config["safe_mode"] = args.safe_mode or not args.aggressive
    config["exploracao"] = exploracao_config
    
    config["pos_exploracao"] = DEFAULT_POS_EXPLORACAO_CONFIG.__dict__.copy()
    config["relatorio"] = DEFAULT_RELATORIO_CONFIG.__dict__.copy()
    
    # Diretório de saída personalizado
    if args.output_dir:
        config["output_dir"] = args.output_dir / args.target.replace(".", "_")
    
    return config

def validate_target(target: str) -> bool:
    """Valida se o alvo é válido"""
    import re
    
    # Regex básico para domínio
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    
    if not re.match(domain_pattern, target):
        return False
    
    # Verifica se não é IP privado ou localhost
    private_patterns = [
        r'^localhost$',
        r'^127\.',
        r'^10\.',
        r'^192\.168\.',
        r'^172\.(1[6-9]|2[0-9]|3[0-1])\.'
    ]
    
    for pattern in private_patterns:
        if re.match(pattern, target):
            print(f"⚠️  Aviso: Alvo parece ser interno/privado: {target}")
            response = input("Continuar mesmo assim? (y/n): ").lower()
            return response == 'y'
    
    return True

def main():
    """Função principal"""
    try:
        # Parse argumentos
        args = parse_arguments()
        
        # Valida alvo
        if not validate_target(args.target):
            print("❌ Alvo inválido ou execução cancelada")
            sys.exit(1)
        
        # Carrega configuração personalizada se fornecida
        custom_config = {}
        if args.config:
            loaded_config = load_custom_config(args.config)
            if loaded_config:
                custom_config = loaded_config
            else:
                sys.exit(1)
        
        # Constrói configuração final
        config = build_config_from_args(args)
        
        # Mescla com configuração personalizada
        if custom_config:
            for key, value in custom_config.items():
                if key in config and isinstance(value, dict):
                    config[key].update(value)
                else:
                    config[key] = value
        
        # Cria instância do Guardian IA
        guardian = GuardianIA(args.target, custom_config=config)
        
        # Determina fases para executar
        if args.phases:
            phases_to_run = parse_phases(args.phases)
            print(f"🎯 Executando fases: {phases_to_run}")
            
            # Executa fases específicas
            phase_methods = {
                1: guardian.execute_phase_1,
                2: guardian.execute_phase_2,
                3: guardian.execute_phase_3,
                4: guardian.execute_phase_4,
                5: guardian.execute_phase_5,
                6: guardian.execute_phase_6
            }
            
            for phase_num in phases_to_run:
                if phase_num in phase_methods:
                    print(f"\n🚀 Executando Fase {phase_num}...")
                    result = phase_methods[phase_num]()
                    
                    if not result.get("sucesso"):
                        print(f"❌ Fase {phase_num} falhou: {result.get('erro', 'Erro desconhecido')}")
                        if not config.get("global", {}).get("safe_mode", True):
                            response = input("Continuar com próximas fases? (y/n): ").lower()
                            if response != 'y':
                                break
                else:
                    print(f"⚠️  Fase {phase_num} inválida (deve ser 1-6)")
        else:
            # Executa todas as fases
            print("🚀 Executando todas as 6 fases do Guardian IA...")
            result = guardian.execute_all_phases()
            
            if result.get("sucesso"):
                print("\n🎉 Execução concluída com sucesso!")
            else:
                print(f"\n❌ Execução falhou: {result.get('erro', 'Erro desconhecido')}")
                sys.exit(1)
        
        print(f"\n📁 Resultados salvos em: {guardian.output_dir}")
        print("🛡️  Guardian IA - Protegendo o invisível. Antecipando o inevitável.")
        
    except KeyboardInterrupt:
        print("\n⚠️  Execução interrompida pelo usuário")
        sys.exit(130)
    except Exception as e:
        logger = get_logger("main")
        logger.error(f"❌ Erro fatal: {e}")
        print(f"❌ Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
