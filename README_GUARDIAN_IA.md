# 🛡️ Guardian IA - Sistema de Pentest Automatizado

> **"Protegendo o invisível. Antecipando o inevitável."**

Guardian IA é um framework completo de penetration testing automatizado que implementa uma metodologia estruturada de 6 fases para análise abrangente de segurança cibernética.

## 📋 Metodologia das 6 Fases

### 🔍 Fase 1: Reconhecimento Estratégico e Coleta de Inteligência
- **Arquivo**: `fase1_recon.py`
- **Objetivo**: Descoberta passiva e ativa de subdomínios e mapeamento inicial da infraestrutura
- **Ferramentas**: subfinder, amass, httpx
- **Saídas**: Lista de subdomínios ativos, tecnologias identificadas, servidores web

### 🎯 Fase 2: Enumeração Ativa e Mapeamento da Superfície de Ataque
- **Arquivo**: `fase2_enum.py`
- **Objetivo**: Descoberta de diretórios, arquivos e parâmetros para mapear a superfície de ataque
- **Ferramentas**: gobuster, ffuf, paramspider
- **Saídas**: Inventário estruturado de alvos (targets.json)

### 🔬 Fase 3: Análise de Vulnerabilidades e Testes de Intrusão
- **Arquivo**: `fase3_vulnerabilidades.py`
- **Objetivo**: Identificação e análise de vulnerabilidades usando múltiplas engines
- **Ferramentas**: SQLMap, Dalfox, Nuclei
- **Saídas**: Relatório detalhado de vulnerabilidades com severidade e PoCs

### 💥 Fase 4: Exploração e Análise de Impacto
- **Arquivo**: `fase4_exploracao.py`
- **Objetivo**: Simulação ética de exploração para demonstrar impacto real
- **Características**: Modo seguro, apenas PoCs, sem danos ao alvo
- **Saídas**: Análise de impacto técnico e de negócio

### 🔄 Fase 5: Pós-Exploração e Análise de Movimento Lateral
- **Arquivo**: `fase5_pos_exploracao.py`
- **Objetivo**: Análise de cenários de escalação e movimento lateral
- **Características**: Cenários hipotéticos, matriz de risco
- **Saídas**: Análise de movimento lateral e recomendações de segmentação

### 📊 Fase 6: Relatório Inteligente e Plano de Ação
- **Arquivo**: `fase6_relatorio_inteligente.py`
- **Objetivo**: Consolidação de dados e geração de relatórios executivos e técnicos
- **Formatos**: JSON, HTML, relatórios executivos
- **Características**: Score de segurança, CVSS, recomendações prioritárias

## 🚀 Como Usar

### Pré-requisitos
```bash
# Instalar ferramentas necessárias
go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
go install -v github.com/projectdiscovery/httpx/cmd/httpx@latest
go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest
go install -v github.com/OJ/gobuster/v3@latest

# Python dependencies
pip install requests beautifulsoup4 urllib3
```

### Execução Completa
```bash
python guardian.py
```

### Execução de Fases Individuais
```python
import fase1_recon

# Configuração personalizada
config = {
    "timeout": 60,
    "max_subdominios": 1000,
    "verificar_httpx": True
}

# Executar apenas Fase 1
resultado = fase1_recon.run("exemplo.com", "./resultados", config)
```

## 📁 Estrutura de Saída

```
resultados/
├── alvo.com/
│   ├── subdominios/
│   │   ├── subfinder_output.txt
│   │   ├── httpx_output.json
│   │   └── ativos_detalhados.json
│   ├── enumeracao/
│   │   ├── diretorios/
│   │   ├── parametros/
│   │   └── targets.json
│   ├── vulnerabilidades/
│   │   ├── sqlmap/
│   │   ├── xss/
│   │   ├── nuclei/
│   │   └── relatorios_fase3/
│   ├── exploits/
│   │   ├── proofs_of_concept/
│   │   └── relatorios_fase4/
│   ├── pos_exploracao/
│   │   ├── escalacao_privilegios/
│   │   ├── movimento_lateral/
│   │   └── relatorios_fase5/
│   └── relatorio_final/
│       ├── json_output/
│       ├── relatorio_YYYYMMDD_HHMMSS.html
│       └── relatorio_completo_YYYYMMDD_HHMMSS.json
```

## 🔧 Configurações Avançadas

### Modo Seguro (Recomendado)
```python
config_seguro = {
    "safe_mode": True,
    "proof_of_concept_only": True,
    "sqlmap_risk": 1,  # Baixo risco
    "timeout": 30
}
```

### Modo Agressivo (Apenas em ambientes controlados)
```python
config_agressivo = {
    "safe_mode": False,
    "sqlmap_risk": 3,  # Alto risco
    "max_exploits_per_vuln": 5,
    "timeout": 120
}
```

## 📊 Exemplo de Saída

### Score de Segurança
- **100/100**: Excelente - Nenhuma vulnerabilidade crítica
- **80-99/100**: Bom - Vulnerabilidades menores identificadas
- **60-79/100**: Moderado - Vulnerabilidades médias presentes
- **40-59/100**: Ruim - Vulnerabilidades altas identificadas
- **0-39/100**: Crítico - Vulnerabilidades críticas presentes

### Níveis de Risco
- **CRÍTICO**: Vulnerabilidades críticas que requerem ação imediata
- **ALTO**: Múltiplas vulnerabilidades altas ou encadeamento possível
- **MÉDIO**: Vulnerabilidades médias ou poucas altas
- **BAIXO**: Apenas vulnerabilidades menores ou informativas

## 🛡️ Características de Segurança

### Modo Ético
- **Safe Mode**: Todas as operações são não-destrutivas por padrão
- **PoC Only**: Apenas demonstrações de conceito, sem exploração real
- **Timeout Controls**: Limites de tempo para evitar sobrecarga do alvo
- **Rate Limiting**: Controle de taxa de requisições

### Compliance
- Respeita robots.txt quando configurado
- Logs detalhados para auditoria
- Relatórios estruturados para compliance
- Recomendações baseadas em frameworks (OWASP, NIST)

## 📈 Métricas e KPIs

### Métricas Técnicas
- Subdomínios descobertos vs. ativos
- Taxa de sucesso de enumeração
- Vulnerabilidades por categoria
- Tempo de execução por fase

### Métricas de Negócio
- Score de segurança geral
- Impacto potencial de negócio
- Priorização de correções
- ROI de investimentos em segurança

## 🔄 Integração e Automação

### CI/CD Integration
```yaml
# Exemplo para GitHub Actions
- name: Guardian IA Security Scan
  run: |
    python guardian.py --target ${{ env.TARGET_DOMAIN }}
    python -c "import json; data=json.load(open('relatorio.json')); exit(1 if data['score_seguranca'] < 80 else 0)"
```

### API Integration
```python
# Exemplo de integração com SIEM
import requests
import json

def enviar_para_siem(relatorio_path):
    with open(relatorio_path) as f:
        data = json.load(f)
    
    # Enviar vulnerabilidades críticas para SIEM
    criticas = [v for v in data['vulnerabilidades'] if v['severidade'] == 'CRITICAL']
    for vuln in criticas:
        requests.post('https://siem.empresa.com/api/alerts', json=vuln)
```

## 🤝 Contribuição

### Adicionando Novas Engines
1. Criar nova classe engine em `fase3_vulnerabilidades.py`
2. Implementar métodos `scan()` e `parse_results()`
3. Adicionar configuração no `VulnConfig`
4. Atualizar documentação

### Adicionando Novos Formatos de Relatório
1. Estender `fase6_relatorio_inteligente.py`
2. Implementar novo método `gerar_relatorio_FORMATO()`
3. Adicionar ao `RelatorioConfig.formato_saida`

## 📞 Suporte

Para questões técnicas ou sugestões de melhorias:
- Verificar logs detalhados em cada fase
- Consultar documentação das ferramentas integradas
- Revisar configurações de rede e permissões

## ⚖️ Disclaimer Legal

**IMPORTANTE**: Este framework deve ser usado apenas em:
- Sistemas próprios ou com autorização explícita
- Ambientes de teste controlados
- Programas de bug bounty autorizados
- Auditorias de segurança contratadas

O uso não autorizado pode violar leis locais e internacionais. Os usuários são responsáveis por garantir conformidade legal antes do uso.

---

**Guardian IA** - Desenvolvido para profissionais de segurança cibernética que buscam automação inteligente e análise abrangente de vulnerabilidades.
