# Guardian IA - Sistema de Pentest Automatizado
# Pacote principal

__version__ = "1.0.0"
__author__ = "Guardian IA Team"
__description__ = "Sistema completo de penetration testing automatizado"

# Imports principais para facilitar o uso
from .core.guardian import GuardianIA
from .phases.fase1_recon import ReconhecimentoEstrategico
from .phases.fase2_enum import EnumeracaoAtiva
from .phases.fase3_vulnerabilidades import AnalisadorVulnerabilidades
from .phases.fase4_exploracao import ExploradorVulnerabilidades
from .phases.fase5_pos_exploracao import AnalisadorPosExploracao
from .phases.fase6_relatorio import GeradorRelatorioInteligente

__all__ = [
    'GuardianIA',
    'ReconhecimentoEstrategico',
    'EnumeracaoAtiva', 
    'AnalisadorVulnerabilidades',
    'ExploradorVulnerabilidades',
    'AnalisadorPosExploracao',
    'GeradorRelatorioInteligente'
]
