# src/utils/command_utils.py - Utilitários para execução de comandos

import subprocess
import shlex
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from ..core.logger import get_logger

class CommandExecutor:
    """Executor seguro de comandos externos"""
    
    def __init__(self, timeout: int = 60, logger_name: str = "command_executor"):
        self.timeout = timeout
        self.logger = get_logger(logger_name)
    
    def execute(self, command: str, cwd: Optional[Path] = None, 
                env: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Executa comando de forma segura
        
        Args:
            command: Comando para executar
            cwd: Diretório de trabalho
            env: Variáveis de ambiente
            
        Returns:
            Dict com resultado da execução
        """
        try:
            self.logger.info(f"Executando comando: {command}")
            
            # Prepara comando
            if isinstance(command, str):
                cmd_args = shlex.split(command)
            else:
                cmd_args = command
            
            # Executa comando
            start_time = time.time()
            
            result = subprocess.run(
                cmd_args,
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=cwd,
                env=env
            )
            
            execution_time = time.time() - start_time
            
            # Prepara resultado
            execution_result = {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": execution_time,
                "command": command
            }
            
            if execution_result["success"]:
                self.logger.info(f"✅ Comando executado com sucesso em {execution_time:.2f}s")
            else:
                self.logger.error(f"❌ Comando falhou (código {result.returncode})")
                if result.stderr:
                    self.logger.error(f"Erro: {result.stderr}")
            
            return execution_result
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"⏰ Timeout após {self.timeout}s")
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": f"Timeout após {self.timeout} segundos",
                "execution_time": self.timeout,
                "command": command
            }
        except Exception as e:
            self.logger.error(f"❌ Erro na execução: {e}")
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "execution_time": 0,
                "command": command
            }
    
    def execute_with_input(self, command: str, input_data: str, 
                          cwd: Optional[Path] = None) -> Dict[str, Any]:
        """
        Executa comando com entrada de dados
        
        Args:
            command: Comando para executar
            input_data: Dados de entrada
            cwd: Diretório de trabalho
            
        Returns:
            Dict com resultado da execução
        """
        try:
            self.logger.info(f"Executando comando com input: {command}")
            
            cmd_args = shlex.split(command) if isinstance(command, str) else command
            
            start_time = time.time()
            
            result = subprocess.run(
                cmd_args,
                input=input_data,
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=cwd
            )
            
            execution_time = time.time() - start_time
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": execution_time,
                "command": command
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erro na execução com input: {e}")
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "execution_time": 0,
                "command": command
            }
    
    def check_tool_availability(self, tool_name: str) -> bool:
        """
        Verifica se uma ferramenta está disponível
        
        Args:
            tool_name: Nome da ferramenta
            
        Returns:
            bool: True se disponível
        """
        try:
            result = self.execute(f"{tool_name} --version")
            return result["success"]
        except:
            try:
                result = self.execute(f"{tool_name} -version")
                return result["success"]
            except:
                try:
                    result = self.execute(f"which {tool_name}")
                    return result["success"]
                except:
                    return False

class ToolRunner:
    """Runner específico para ferramentas de pentest"""
    
    def __init__(self, timeout: int = 300):
        self.executor = CommandExecutor(timeout)
        self.logger = get_logger("tool_runner")
    
    def run_subfinder(self, domain: str, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Executa subfinder"""
        cmd = f"subfinder -d {domain} -silent"
        if output_file:
            cmd += f" -o {output_file}"
        
        return self.executor.execute(cmd)
    
    def run_httpx(self, input_file: Path, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Executa httpx"""
        cmd = f"httpx -l {input_file} -silent -tech-detect -status-code"
        if output_file:
            cmd += f" -o {output_file}"
        
        return self.executor.execute(cmd)
    
    def run_nuclei(self, target: str, output_file: Optional[Path] = None, 
                   severity: List[str] = None) -> Dict[str, Any]:
        """Executa nuclei"""
        cmd = f"nuclei -target {target} -silent"
        
        if severity:
            cmd += f" -severity {','.join(severity)}"
        
        if output_file:
            cmd += f" -o {output_file}"
        
        return self.executor.execute(cmd)
    
    def run_gobuster(self, url: str, wordlist: Path, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Executa gobuster"""
        cmd = f"gobuster dir -u {url} -w {wordlist} -q"
        
        if output_file:
            cmd += f" -o {output_file}"
        
        return self.executor.execute(cmd)
    
    def run_sqlmap(self, url: str, output_dir: Optional[Path] = None, 
                   risk: int = 1, level: int = 1) -> Dict[str, Any]:
        """Executa SQLMap"""
        cmd = f"sqlmap -u '{url}' --batch --risk={risk} --level={level}"
        
        if output_dir:
            cmd += f" --output-dir={output_dir}"
        
        return self.executor.execute(cmd)
    
    def run_dalfox(self, url: str, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Executa Dalfox"""
        cmd = f"dalfox url {url} --silence"
        
        if output_file:
            cmd += f" -o {output_file}"
        
        return self.executor.execute(cmd)

def validate_command_safety(command: str, forbidden_patterns: List[str] = None) -> bool:
    """
    Valida se um comando é seguro para execução
    
    Args:
        command: Comando para validar
        forbidden_patterns: Padrões proibidos
        
    Returns:
        bool: True se seguro
    """
    if not forbidden_patterns:
        forbidden_patterns = [
            "rm -rf", "format", "del /", "rmdir /s", 
            "DROP TABLE", "DELETE FROM", "shutdown", "reboot"
        ]
    
    command_lower = command.lower()
    
    for pattern in forbidden_patterns:
        if pattern.lower() in command_lower:
            return False
    
    return True

def escape_shell_argument(arg: str) -> str:
    """
    Escapa argumentos para shell de forma segura
    
    Args:
        arg: Argumento para escapar
        
    Returns:
        str: Argumento escapado
    """
    return shlex.quote(arg)

def build_command(base_cmd: str, args: Dict[str, Any]) -> str:
    """
    Constrói comando de forma segura
    
    Args:
        base_cmd: Comando base
        args: Argumentos
        
    Returns:
        str: Comando completo
    """
    cmd_parts = [base_cmd]
    
    for key, value in args.items():
        if value is True:
            cmd_parts.append(f"--{key}")
        elif value is False or value is None:
            continue
        else:
            cmd_parts.append(f"--{key}")
            cmd_parts.append(escape_shell_argument(str(value)))
    
    return " ".join(cmd_parts)
