# Individuals

Andres <PERSON>, <atarasco(at)gmail.com>
* for suggesting a feature

Santiago Accurso, <saccurso(at)skygear.com.ar>
* for reporting a bug

<PERSON>, <syed(at)syedafzal.in>
* for contributing a WAF script varnish.py

<PERSON><PERSON>, <z<PERSON><PERSON><PERSON><PERSON>(at)gmail.com>
* for suggesting a couple of features

<PERSON><PERSON>, <seyi.akin(at)gmail.com>
* for reporting a couple of bugs

<PERSON>, <david.alvarez.s(at)gmail.com>
* for reporting a bug

<PERSON>, <sergioalexandre.alves(at)gmail.com>
* for reporting a bug

<PERSON>, <darkc0de(at)live.com.ph>
* for reporting a bug

<PERSON>, <chip(at)sqlsecurity.com>
* for his excellent work maintaining the SQL Server versions database at SQLSecurity.com and permission to implement the update feature taking data from his site

<PERSON> Andy, <teh.one(at)hotmail.com>
* for suggesting a feature

<PERSON><PERSON><PERSON>, <otavioarj(at)gmail.com>
* for reporting a minor bug

<PERSON>, <simonb(at)sec-1.com>
* for reporting some bugs

<PERSON>, <<PERSON><PERSON>nett(at)trustwave.com>
* for organizing the ModSecurity SQL injection challenge, http://modsecurity.org/demo/challenge.html

<PERSON><PERSON>, <emiliano(at)7espejos.com>
* for reporting a minor bug

Daniele Bellucci, <daniele.bellucci(at)gmail.com>
* for starting sqlmap project and developing it between July and August 2006

Sebastian Bittig, <s.bittig(at)r-tec.net> and the rest of the team at r-tec IT Systeme GmbH
* for contributing the DB2 support initial patch: fingerprint and enumeration

Anthony Boynes, <aboynes(at)gmail.com>
* for reporting several bugs

Marcelo Toscani Brandao
* for reporting a bug

Velky Brat, <velkybrat(at)gmail.com>
* for suggesting a minor enhancement to the bisection algorithm

James Briggs, <james.briggs(at)ngssecure.com>
* for suggesting a minor enhancement

Gianluca Brindisi, <g(at)brindi.si>
* for reporting a couple of bugs

Jack Butler, <fattredd(at)hotmail.com>
* for contributing the sqlmap site favicon

Ulisses Castro, <uss.thebug(at)gmail.com>
* for reporting a bug

Roberto Castrogiovanni, <castrogiovanni.roberto(at)gmail.com>
* for reporting a minor bug

Cesar Cerrudo, <cesar(at)argeniss.com>
* for his Windows access token kidnapping tool Churrasco included in sqlmap tree as a contrib library and used to run the stand-alone payload stager on the target Windows machine as SYSTEM user if the user wants to perform a privilege escalation attack, http://www.argeniss.com/research/TokenKidnapping.pdf

Karl Chen, <quarl(at)cs.berkeley.edu>
* for contributing the initial multi-threading patch for the inference algorithm

Y P Chien, <ypchien(at)cox.net>
* for reporting a minor bug

Pierre Chifflier, <pollux(at)debian.org> and Mark Hymers, <ftpmaster(at)debian.org>
* for uploading and accepting the sqlmap Debian package to the official Debian project repository

Hysia Chow <hysia(at)icloud.com>
* for contributing a couple of WAF scripts

Chris Clements, <cclements(at)flatearth.net>
* for reporting a couple of bugs

John Cobb, <johnc(at)nobytes.com>
* for reporting a minor bug

Andreas Constantinides, <megahz(at)megahz.org>
* for reporting a minor bug

Andre Costa, <andre.investorsclub(at)gmail.com>
* for reporting a minor bug
* for suggesting a minor enhancement

Ulises U. Cune, <ulises2k(at)gmail.com>
* for reporting a bug

Alessandro Curio, <alessandro.curio(at)gmail.com>
* for reporting a minor bug

Alessio Dalla Piazza, <alessio.dallapiazza(at)gmail.com>
* for reporting a couple of bugs

Alexis Danizan, <alexis.danizan(at)synacktiv.com>
* for contributing support for ClickHouse

Sherif El-Deeb, <archeldeeb(at)gmail.com>
* for reporting a minor bug

Thomas Etrillard, <thomas.etrillard(at)synacktiv.com>
* for contributing the IBM DB2 error-based payloads (RAISE_ERROR)

Stefano Di Paola, <stefano.dipaola(at)wisec.it>
* for suggesting good features

Mosk Dmitri, <ya(at)darkbyte.ru>
* for reporting a minor bug

Meng Dong, <whenov(at)gmail.com>
* for contributing a code for Waffit integration

Carey Evans, <careye(at)spamcop.net>
* for his fcrypt module that allows crypt(3) support
    on Windows platforms

Shawn Evans, <shawndevans(at)gmail.com>
* for suggesting an idea for one tamper script, greatest.py

Adam Faheem, <faheem.adam(at)is.co.za>
* for reporting a few bugs

James Fisher, <www(at)sittinglittleduck.com>
* for contributing two very good feature requests
* for his great tool too brute force directories and files names on web/application servers, DirBuster, http://tinyurl.com/dirbuster

Jim Forster, <jimforster(at)goldenwest.com>
* for reporting a bug

Rong-En Fan, <rafan(at)freebsd.org>
* for committing the sqlmap 0.5 port to the official FreeBSD project repository

Giorgio Fedon, <giorgio.fedon(at)gmail.com>
* for suggesting a speed improvement for bisection algorithm
* for reporting a bug when running against Microsoft SQL Server 2005

Kasper Fons, <thefeds(at)mail.dk>
* for reporting several bugs

Alan Franzoni, <alan.franzoni(at)gmail.com>
* for helping out with Python subprocess library

Harold Fry, <harold(at)violaceo.us>
* for suggesting a minor enhancement

Daniel G. Gamonal, <lgrecol(at)gmail.com>
* for reporting a minor bug

Marcos Mateos Garcia, <mmateos(at)germinus.com>
* for reporting a minor bug

Andrew Gecse, <andrew.gecse(at)upcmail.hu>
* for reporting a minor issue

Ivan Giacomelli, <truemilk(at)insiberia.net>
* for reporting a bug
* for suggesting a minor enhancement
* for reviewing the documentation

Dimitris Giannitsaros, <daremon(at)gmail.com>
* for contributing a REST-JSON API client

Nico Golde, <nico(at)ngolde.de>
* for reporting a couple of bugs

Oliver Gruskovnjak, <oliver.gruskovnjak(at)gmail.com>
* for reporting a bug
* for contributing a minor patch

Davide Guerri, <d.guerri(at)caspur.it>
* for suggesting an enhancement

Dan Guido, <dguido(at)gmail.com>
* for promoting sqlmap in the context of the Penetration Testing and Vulnerability Analysis class at the Polytechnic University of New York, http://isisblogs.poly.edu/courses/pentest/

David Guimaraes, <skysbsb(at)gmail.com>
* for reporting considerable amount of bugs
* for suggesting several features

Chris Hall, <chris.hall(at)mod10.net>
* for coding the prettyprint.py library

Tate Hansen, <tate(at)clearnetsec.com>
* for donating to sqlmap development

Mario Heiderich, <mario.heiderich(at)gmail.com>
Christian Matthies, <ch0012(at)gmail.com>
Lars H. Strojny, <lars(at)strojny.net>
* for their great tool PHPIDS included in sqlmap tree as a set of rules for testing payloads against IDS detection, https://github.com/PHPIDS/PHPIDS

Kristian Erik Hermansen, <kristian.hermansen(at)gmail.com>
* for reporting a bug
* for donating to sqlmap development

Alexander Hagenah, <ah(at)primepage.de>
* for reporting a minor bug

Dennis Hecken, <mail(at)8dh.de>
* for reporting a minor bug

Choi Ho, <counterhacker815(at)gmail.com>
* for reporting a minor bug

Jorge Hoya, <aquinadie(at)gmail.com>
* for suggesting a minor enhancement

Will Holcomb, <wholcomb(at)gmail.com>
* for his MultipartPostHandler class to handle multipart POST forms and permission to include it within sqlmap source code

Daniel Huckmann, <sanitybit(at)gmail.com>
* for reporting a couple of bugs

Daliev Ilya, <daliser(at)yandex.ru>
* for reporting a bug

Mehmet İnce, <mehmet(at)mehmetince.net>
* for contributing a tamper script xforwardedfor.py

Jovon Itwaru, <jovon.itwaru(at)gmail.com>
* for reporting a minor bug

Prashant Jadhav, <prashantjadhav.82(at)gmail.com>
* for reporting a bug

Dirk Jagdmann, <doj(at)cubic.org>
* for reporting a typo in the documentation

Luke Jahnke, <luke.jahnke(at)gmail.com>
* for reporting a bug when running against MySQL < 5.0

Andrew Kitis <andrew.kitis(at)gmail.com>
* for contributing a tamper script lowercase.py

David Klein, <david.klein(at)ipfocus.com.au>
* for reporting a minor code improvement

Sven Klemm, <sven(at)c3d2.de>
* for reporting two minor bugs with PostgreSQL

Anant Kochhar, <anant.kochhar(at)secureyes.net>
* for providing with feedback on the user's manual

Dmitriy Kononov, <dmitriyknnv(at)gmail.com>
* for reporting a minor bug

Alexander Kornbrust, <ak(at)red-database-security.com>
* for reporting a couple of bugs

Krzysztof Kotowicz, <kkotowicz(at)gmail.com>
* for reporting a minor bug

Nicolas Krassas, <krasn(at)deventum.com>
* for reporting a couple of bugs

Oliver Kuckertz, <oliver.kuckertz(at)mologie.de>
* for contributing a minor patch

Alex Landa, <landa.alex86(at)gmail.com>
* for contributing a patch adding beta support for XML output

Guido Landi, <lists(at)keamera.org>
* for reporting a couple of bugs
* for the great technical discussions
* for Microsoft SQL Server 2000 and Microsoft SQL Server 2005 'sp_replwritetovarbin' stored procedure heap-based buffer overflow (MS09-004) exploit development
* for presenting with Bernardo at SOURCE Conference 2009 in Barcelona (Spain) on September 21, 2009 and at CONfidence 2009 in Warsaw (Poland) on November 20, 2009

Lee Lawson, <Lee.Lawson(at)dns.co.uk>
* for reporting a minor bug

John J. Lee, <jjl(at)pobox.com> and others
* for developing the clientform Python library used by sqlmap to parse forms when --forms switch is specified

Nico Leidecker, <nico(at)leidecker.info>
* for providing with feedback on a few features
* for reporting a couple of bugs
* for his great tool icmpsh included in sqlmap tree to get a command prompt via an out-of-band tunnel over ICMP, http://leidecker.info/downloads/icmpsh.zip

Gabriel Lima, <pato(at)bugnet.com.br>
* for reporting a couple of bugs

Svyatoslav Lisin, <sel(at)3d-tech.ru>
* for suggesting a minor feature

Miguel Lopes, <theoverblue(at)gmail.com>
* for reporting a minor bug

Truong Duc Luong, <luongductruong(at)gmail.com>
* for reporting a minor bug

Pavol Luptak, <pavol.luptak(at)nethemba.com>
* for reporting a bug when injecting on a POST data parameter

Till Maas, <opensource(at)till.name>
* for suggesting a minor feature

Michael Majchrowicz, <mmajchrowicz(at)gmail.com>
* for extensively beta-testing sqlmap on various MySQL DBMS
* for providing really appreciated feedback
* for suggesting a lot of ideas and features

Vinícius Henrique Marangoni, <vinicius_marangoni1(at)hotmail.com>
* for contributing a Portuguese translation of README.md

Francesco Marano, <francesco.mrn24(at)gmail.com>
* for contributing the Microsoft SQL Server/Sybase error-based - Stacking (EXEC) payload

Ahmad Maulana, <matdhule(at)gmail.com>
* for contributing a tamper script halfversionedmorekeywords.py

Ferruh Mavituna, <ferruh(at)mavituna.com>
* for exchanging ideas on the implementation of a couple of features

David McNab, <david(at)conscious.co.nz>
* for his XMLObject module that allows XML files to be operated on  like Python objects

Spencer J. McIntyre, <smcintyre(at)securestate.com>
* for reporting a minor bug
* for contributing a patch for OS fingerprinting on DB2

Brad Merrell, <bradmer12(at)gmail.com>
* for reporting a minor bug

Michael Meyer, <m.meyer2k(at)gmail.com>
* for suggesting a minor feature

Enrico Milanese, <enricomilanese(at)gmail.com>
* for reporting a minor bug
* for sharing some ideas for the PHP backdoor

Liran Mimoni, <reactor.leet(at)gmail.com>
* for reporting a minor bug

Marco Mirandola, <mmmccc0(at)gmail.com>
* for reporting a minor bug

Devon Mitchell, <devon.mitchell1988(at)yahoo.com>
* for reporting a minor bug

Anton Mogilin, <azarmaster81(at)yahoo.com>
* for reporting a few bugs

Sergio Molina, <smolina(at)wpr.es>
* for reporting a minor bug

Anastasios Monachos, <anastasiosm(at)gmail.com>
* for providing some useful data
* for suggesting a feature
* for reporting a couple of bugs

Kirill Morozov, <l0rda(at)l0rda.biz>
* for reporting a bug
* for suggesting a feature

Alejo Murillo Moya, <alex(at)65535.com>
* for reporting a minor bug
* for suggesting a few features

Yonny Mutai, <yonnym(at)googlemail.com>
* for reporting a minor bug

Roberto Nemirovsky, <roberto.paes(at)gmail.com>
* for pointing out some enhancements

Sebastian Nerz, <sebastian.nerz(at)syss.de>
* for reporting a (potential) vulnerability in --eval

Simone Onofri, <simone.onofri(at)gmail.com>
* for patching the PHP web backdoor to make it work properly also on Windows

Michele Orru, <michele.orru(at)antisnatchor.com>
* for reporting a couple of bug
* for suggesting ideas on how to implement the RESTful API

Shaohua Pan, <pan(at)knownsec.com>
* for reporting several bugs
* for suggesting a few features

Antonio Parata, <s4tan(at)ictsc.it>
* for sharing some ideas for the PHP backdoor

Adrian Pastor, <ap(at)gnucitizen.org>
* for donating to sqlmap development

Christopher Patten, <cpatten(at)sunera.com>
* for reporting a bug in the blind SQL injection bisection algorithm

Zack Payton, <zack.payton(at)executiveinstruments.com>
* for reporting a minor bug

Jaime Penalba, <nighterman(at)painsec.com>
* for contributing a patch for INSERT/UPDATE generic boundaries

Pedrito Perez, <0ark1ang3l(at)gmail.com>
* for reporting a couple of bugs

Brandon Perry, <bperry.volatile(at)gmail.com>
* for reporting a couple of bugs

Travis Phillips, <perfect_insanity2004(at)yahoo.com>
* for suggesting a minor enhancement

Mark Pilgrim, <mark(at)diveintomark.org>
* for porting chardet package (Universal Encoding Detector) to Python

Steve Pinkham, <steve.pinkham(at)gmail.com>
* for suggesting a feature
* for contributing a new SQL injection vector (MSSQL time-based blind)
* for donating to sqlmap development

Adam Pridgen, <adam.pridgen(at)gmail.com>
* for suggesting some features

Luka Pusic, <luka(at)pusic.si>
* for reporting a couple of bugs

Ole Rasmussen, <olerass(at)gmail.com>
* for reporting a bug
* for suggesting a feature

Alberto Revelli, <r00t(at)northernfortress.net>
* for inspiring to write sqlmap user's manual in SGML
* for his great Microsoft SQL Server take over tool, sqlninja, http://sqlninja.sourceforge.net

David Rhoades, <david.rhoades(at)mavensecurity.com>
* for reporting a bug

Andres Riancho, <andres.riancho(at)gmail.com>
* for beta-testing sqlmap
* for reporting a bug and suggesting some features
* for including sqlmap in his great web application audit and attack framework, w3af, http://w3af.sourceforge.net
* for suggesting a way for handling DNS caching

Jamie Riden, <jamie.riden(at)ngssecure.com>
* for reporting a minor bug

Alexander Rigbo, <alex(at)rigbo.se>
* for contributing a minor patch

Antonio Riva, <antonio.riva(at)gmail.com>
* for reporting a bug when running with python 2.5

Ethan Robish, <ethan.robish(at)gmail.com>
* for reporting a bug

Levente Rog, <levidos(at)gmail.com>
* for reporting a minor bug

Andrea Rossi, <andyroyalbattle(at)yahoo.it>
* for reporting a minor bug
* for suggesting a feature

Frederic Roy, <frederic.roy(at)telindus.fr>
* for reporting a couple of bugs

Vladimir Rutsky, <rutsky.vladimir(at)gmail.com>
* for suggesting a couple of minor enhancements

Richard Safran, <allapplyhere(at)yahoo.com>
* for donating the sqlmap.org domain

Tomoyuki Sakurai, <cherry(at)trombik.org>
* for submitting to the FreeBSD project the sqlmap 0.5 port

Roberto Salgado, <lightos(at)gmail.com>
* for contributing considerable amount of tamper scripts

Pedro Jacques Santos Santiago, <pedro__jacques(at)hotmail.com>
* for reporting considerable amount of bugs

Marek Sarvas, <marek.sarvas(at)gmail.com>
* for reporting several bugs

Philippe A. R. Schaeffer, <schaeff(at)compuphil.de>
* for reporting a minor bug

Henri Salo <henri(at)nerv.fi>
* for a donation

Mohd Zamiri Sanin, <zamiri.sanin(at)gmail.com>
* for reporting a minor bug

Jorge Santos, <jorge_a_santos(at)hotmail.com>
* for reporting a minor bug

Sven Schluter, <sschlueter(at)netzwerk.cc>
* for contributing a patch
* for waiting a number of seconds between each HTTP request

Ryan Sears, <rdsears(at)mtu.edu>
* for suggesting a couple of enhancements
* for donating to sqlmap development

Uemit Seren, <uemit.seren(at)gmail.com>
* for reporting a minor adjustment when running with python 2.6

Shane Sewell, <ssewell(at)gmail.com>
* for suggesting a feature

Ahmed Shawky, <ahmed(at)isecur1ty.org>
* for reporting a major bug with improper handling of parameter values
* for reporting a bug

Brian Shura, <bshura(at)appsecconsulting.com>
* for reporting a bug

Sumit Siddharth, <sid(at)notsosecure.com>
* for sharing ideas on the implementation of a couple of features

Andre Silva, <andreoaz(at)gmail.com>
* for reporting a bug

Benjamin Silva H. <silva96(at)gmail.com>
* for reporting a bug

Duarte Silva <duarte.silva(at)serializing.me>
* for reporting a couple of bugs

M Simkin, <mlsimkin(at)cox.net>
* for suggesting a feature

Konrads Smelkovs, <konrads(at)smelkovs.com>
* for reporting a few bugs in --sql-shell and --sql-query on Microsoft SQL Server

Chris Spencer, <chris.spencer(at)ngssecure.com>
* for reviewing the user's manual grammar

Michael D. Stenner, <mstenner(at)linux.duke.edu>
* for his keepalive module that allows handling of persistent HTTP 1.1 keep-alive connections

Marek Stiefenhofer, <m.stiefenhofer(at)r-tec.net>
* for reporting a few bugs

Jason Swan, <jasoneswan(at)gmail.com>
* for reporting a bug when enumerating columns on Microsoft SQL Server
* for suggesting a couple of improvements

Chilik Tamir, <phenoman(at)gmail.com>
* for contributing a patch for initial support SOAP requests

Alessandro Tanasi, <alessandro(at)tanasi.it>
* for extensively beta-testing sqlmap
* for suggesting many features and reporting some bugs
* for reviewing the documentation

Andres Tarasco, <atarasco(at)gmail.com>
* for contributing good feedback

Tom Thumb, <k1971(at)live.co.uk>
* for reporting a major bug

Kazim Bugra Tombul, <mhackmail(at)gmail.com>
* for reporting a minor bug

Efrain Torres, <et(at)metasploit.com>
* for helping out to improve the Metasploit Framework sqlmap auxiliary module and for committing it on the Metasploit official subversion repository
* for his great Metasploit WMAP Framework

Jennifer Torres, <jtorresf42(at)gmail.com>
* for contributing a tamper script luanginx.py

Sandro Tosi, <matrixhasu(at)gmail.com>
* for helping to create sqlmap Debian package correctly

Jacco van Tuijl, <jaccovantuijl(at)gmail.com>
* for reporting several bugs

Vitaly Turenko, <dsu(at)dsu.com.ua>
* for reporting a bug

Augusto Urbieta, <x2xpy50(at)gmail.com>
* for reporting a minor bug

Bedirhan Urgun, <bedirhanurgun(at)gmail.com>
* for reporting a few bugs
* for suggesting some features and improvements
* for benchmarking sqlmap in the context of his SQL injection benchmark project, OWASP SQLiBench, http://code.google.com/p/sqlibench

Kyprianos Vasilopoulos, <kyprianos.vasilopoulos(at)gmail.com>
* for reporting a couple of minor bugs

Vlado Velichkovski, <ketejadam(at)hotmail.com>
* for reporting considerable amount of bugs
* for suggesting an enhancement

Johnny Venter, <johnny.venter(at)zoho.com>
* for reporting a couple of bugs

Carlos Gabriel Vergara, <carlosgabrielvergara(at)gmail.com>
* for suggesting couple of good features

Patrick Webster, <patrick(at)aushack.com>
* for suggesting an enhancement
* for donating to sqlmap development (from OSI.Security)

Ed Williams, <ed.williams(at)ngssecure.com>
* for suggesting a minor enhancement

Anthony Zboralski, <anthony.zboralski(at)bellua.com>
* for providing with detailed feedback
* for reporting a few minor bugs
* for donating to sqlmap development

Thierry Zoller, <thierry(at)zoller.lu>
* for reporting a couple of major bugs

Zhen Zhou, <zhouzhenster(at)gmail.com>
* for suggesting a feature

-insane-, <insane_(at)gmx.de>
* for reporting a minor bug

1ndr4 joe, <c0d3w4st3r(at)gmail.com>
* for reporting a couple of bugs

abc abc, <biedimc(at)gmx.net>
* for reporting a minor bug

Abuse 007, <abuse007(at)gmail.com>
* for reporting a bug

agix, <<EMAIL>>
* for contributing the file upload via certutil.exe functionality

Alex, <m3zero(at)gmail.com>
* for reporting a minor bug

anonymous anonymous, <tmp(at)2ch.so>
* for reporting a couple of bugs

bamboo, <roberthacksley(at)gmail.com>
* for reporting a couple of bugs

Brandon E., <brandonpoc(at)gmail.com>
* for reporting a bug

black zero, <timeisflowing(at)gmail.com>
* for reporting a minor bug

blueBoy, <blueboy4444(at)gmail.com>
* for reporting a bug

buawig, <buawig(at)gmail.com>
* for reporting considerable amount of bugs

Bugtrace, <bugtrace(at)gmail.com>
* for reporting several bugs

cats, <dump(at)alcor.se>
* for reporting a couple of bugs

Christian S, <christian_s(at)linuxmail.org>
* for reporting a minor bug

clav, <elclav(at)gmail.com>
* for reporting a minor bug

dragoun dash, <dragoun.dash(at)gmail.com>
* for reporting a minor bug

flsf, <<EMAIL>>
* for contributing WAF scripts 360.py, anquanbao.py, baidu.py, safedog.py
* for contributing a minor patch

fufuh, <fufuh(at)users.sourceforge.net>
* for reporting a bug when running on Windows

Hans Wurst, <wurstwass0r(at)googlemail.com>
* for reporting a couple of bugs

Hysia, <hysia(at)huorui.net>
* for contributing a Chinese translation of README.md

james, <james(at)ev6.net>
* for reporting a bug

Joe "Pragmatk", <pragmatk(at)gmail.com>
* for reporting a few bugs

John Smith, <tixos(at)live.com>
* for reporting several bugs
* for suggesting some features

m4l1c3, <malice.anon(at)gmail.com>
* for reporting considerable amount of bugs

mariano, <marianoso(at)gmail.com>
* for reporting a bug

mitchell, <mitchell(at)tufala.net>
* for reporting a few bugs

Nadzree, <nadzree(at)bake180.com>
* for reporting a minor bug

nightman, <nightman(at)email.de>
* for reporting considerable amount of bugs

Oso Dog osodog123(at)yahoo.com
* for reporting a minor bug

pacman730, <pacman730(at)users.sourceforge.net>
* for reporting a bug

pentestmonkey, <pentestmonkey(at)pentestmonkey.net>
* for reporting several bugs
* for suggesting a few minor enhancements

Phat R., <phatthanaphol(at)gmail.com>
* for reporting a few bugs

Phil P, <(at)superevr>
* for suggesting a minor enhancement

ragos, <ragos(at)joker.ms>
* for reporting a minor bug

rmillet, <rmillet42(at)gmail.com>
* for reporting a bug

Rub3nCT, <rub3nct(at)gmail.com>
* for reporting a minor bug

sapra, <amanistaken(at)gmail.com>
* for helping out with Python multiprocessing library on MacOS

shiftzwei, <shiftzwei(at)gmail.com>
* for reporting a couple of bugs

smith, <esmyl911(at)gmail.com>
* for reporting a minor bug

Soma Cruz, <oleg.kupreev(at)gmail.com>
* for reporting a minor bug

Spiros94, <cont(at)eyrhka.gr>
* for contributing a Greek translation of README.md

Stuffe, <stuffe.dk(at)gmail.com>
* for reporting a minor bug and a feature request

Sylphid, <sylphid.su(at)sti.com.tw>
* for suggesting some features

syssecurity.info, <syssecurity7(at)googlemail.com>
* for reporting a minor bug

This LittlePiggy, <thislittlepiggyhadroastbeef(at)hotmail.com>
* for reporting a minor bug

ToR, <sstidus(at)email.it>
* for reporting considerable amount of bugs
* for suggesting a feature

ultramegaman, <seclists(at)ultramegaman.com>
* for reporting a minor bug

Vinicius, <viniciusmaxdaloop(at)gmail.com>
* for reporting a minor bug

virusdefender
* for contributing WAF scripts safeline.py

w8ay
* for contributing an implementation for chunked transfer-encoding (switch --chunked)

wanglei, <wanglei(at)17uxi.cn>
* for reporting a minor bug

warninggp, <warninggp(at)gmail.com>
* for reporting a few minor bugs

x, <deep_freeze(at)mail.ru>
* for reporting a bug

zhouhx, <zhouhx(at)knownsec.com>
* for contributing a minor patch

# Organizations

Black Hat team, <info(at)blackhat.com>
* for the opportunity to present my research titled 'Advanced SQL injection to operating system full control' at Black Hat Europe 2009 Briefings on April 16, 2009 in Amsterdam (NL). I unveiled and demonstrated some of the sqlmap 0.7 release candidate version new features during my presentation
 * Homepage: http://goo.gl/BKfs7
 * Slides: http://goo.gl/Dh65t
 * White paper: http://goo.gl/spX3N

SOURCE Conference team, <press(at)sourceconference.com>
* for the opportunity to present my research titled 'Expanding the control over the operating system from the database' at SOURCE Conference 2009 on September 21, 2009 in Barcelona (ES). I unveiled and demonstrated some of the sqlmap 0.8 release candidate version new features during my presentation
 * Homepage: http://goo.gl/IeXV4
 * Slides: http://goo.gl/OKnfj

AthCon Conference team, <cfp(at)athcon.org>
* for the opportunity to present my research titled 'Got database access? Own the network!' at AthCon Conference 2010 on June 3, 2010 in Athens (GR). I unveiled and demonstrated some of the sqlmap 0.8 version features during my presentation
 * Homepage: http://goo.gl/Fs71I
 * Slides: http://goo.gl/QMfjO

Metasploit Framework development team, <msfdev(at)metasploit.com>
* for their powerful tool Metasploit Framework, used by sqlmap, among others things, to create the shellcode and establish an out-of-band connection between sqlmap and the database server
 * Homepage: http://www.metasploit.com

OWASP Board, <info(at)owasp.org>
* for sponsoring part of the sqlmap development in the context of OWASP Spring of Code 2007
 * Homepage: http://www.owasp.org
