# ===============================================================================
# GUARDIAN IA - INSTALACAO COMPLETA DE FERRAMENTAS DE PENTEST
# "Protegendo o invisivel. Antecipando o inevitavel."
# ===============================================================================

Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "GUARDIAN IA - INSTALACAO DE FERRAMENTAS DE PENTEST" -ForegroundColor Yellow
Write-Host "Protegendo o invisivel. Antecipando o inevitavel." -ForegroundColor Gray
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""

# Verifica se esta executando como administrador
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERRO: Este script precisa ser executado como Administrador!" -ForegroundColor Red
    Write-Host "Clique com botao direito no PowerShell e selecione 'Executar como administrador'" -ForegroundColor Yellow
    pause
    exit 1
}

# Função para verificar se uma ferramenta está instalada
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Função para instalar via Chocolatey
function Install-WithChoco($package, $name) {
    Write-Host "📦 Instalando $name via Chocolatey..." -ForegroundColor Yellow
    try {
        choco install $package -y
        if (Test-Command $package) {
            Write-Host "✅ $name instalado com sucesso!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️  $name pode ter sido instalado, mas não está no PATH" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ Erro ao instalar $name via Chocolatey" -ForegroundColor Red
        return $false
    }
}

# Função para instalar via Go
function Install-WithGo($package, $name) {
    Write-Host "🔧 Instalando $name via Go..." -ForegroundColor Yellow
    try {
        go install $package
        Write-Host "✅ $name instalado com sucesso!" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Erro ao instalar $name via Go" -ForegroundColor Red
        return $false
    }
}

# Função para instalar via pip
function Install-WithPip($package, $name) {
    Write-Host "🐍 Instalando $name via pip..." -ForegroundColor Yellow
    try {
        pip install $package
        Write-Host "✅ $name instalado com sucesso!" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Erro ao instalar $name via pip" -ForegroundColor Red
        return $false
    }
}

Write-Host "🔍 Verificando pré-requisitos..." -ForegroundColor Cyan

# Verifica se o Chocolatey está instalado
if (-not (Test-Command "choco")) {
    Write-Host "📦 Instalando Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    if (-not (Test-Command "choco")) {
        Write-Host "❌ Falha ao instalar Chocolatey. Instalação manual necessária." -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Chocolatey instalado!" -ForegroundColor Green
}

# Verifica se o Go está instalado
if (-not (Test-Command "go")) {
    Write-Host "🔧 Instalando Go..." -ForegroundColor Yellow
    Install-WithChoco "golang" "Go"
    
    # Atualiza PATH para incluir Go
    $env:PATH += ";C:\Program Files\Go\bin;$env:USERPROFILE\go\bin"
    [Environment]::SetEnvironmentVariable("PATH", $env:PATH, [EnvironmentVariableTarget]::User)
    
    if (-not (Test-Command "go")) {
        Write-Host "❌ Go não foi instalado corretamente. Verifique manualmente." -ForegroundColor Red
        exit 1
    }
}

# Verifica se o Python/pip está instalado
if (-not (Test-Command "pip")) {
    Write-Host "🐍 Instalando Python..." -ForegroundColor Yellow
    Install-WithChoco "python" "Python"
    
    if (-not (Test-Command "pip")) {
        Write-Host "❌ Python/pip não foi instalado corretamente. Verifique manualmente." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🚀 Iniciando instalação das ferramentas de pentest..." -ForegroundColor Cyan
Write-Host ""

# ===============================================================================
# FASE 1 - FERRAMENTAS DE RECONHECIMENTO
# ===============================================================================
Write-Host "📍 FASE 1 - FERRAMENTAS DE RECONHECIMENTO" -ForegroundColor Magenta

$tools_fase1 = @(
    @{name="subfinder"; go_package="github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest"},
    @{name="amass"; go_package="github.com/owasp-amass/amass/v4/...@master"},
    @{name="assetfinder"; go_package="github.com/tomnomnom/assetfinder@latest"},
    @{name="findomain"; choco_package="findomain"},
    @{name="httpx"; go_package="github.com/projectdiscovery/httpx/cmd/httpx@latest"},
    @{name="gobuster"; go_package="github.com/OJ/gobuster/v3@latest"},
    @{name="gowitness"; go_package="github.com/sensepost/gowitness@latest"}
)

foreach ($tool in $tools_fase1) {
    if (Test-Command $tool.name) {
        Write-Host "✅ $($tool.name) já está instalado" -ForegroundColor Green
    } else {
        if ($tool.go_package) {
            Install-WithGo $tool.go_package $tool.name
        } elseif ($tool.choco_package) {
            Install-WithChoco $tool.choco_package $tool.name
        }
    }
}

# ===============================================================================
# FASE 2 - FERRAMENTAS DE ENUMERAÇÃO
# ===============================================================================
Write-Host ""
Write-Host "📍 FASE 2 - FERRAMENTAS DE ENUMERAÇÃO" -ForegroundColor Magenta

$tools_fase2 = @(
    @{name="feroxbuster"; choco_package="feroxbuster"},
    @{name="katana"; go_package="github.com/projectdiscovery/katana/cmd/katana@latest"},
    @{name="gospider"; go_package="github.com/jaeles-project/gospider@latest"},
    @{name="hakrawler"; go_package="github.com/hakluke/hakrawler@latest"},
    @{name="paramspider"; pip_package="paramspider"},
    @{name="arjun"; pip_package="arjun"},
    @{name="gf"; go_package="github.com/tomnomnom/gf@latest"}
)

foreach ($tool in $tools_fase2) {
    if (Test-Command $tool.name) {
        Write-Host "✅ $($tool.name) já está instalado" -ForegroundColor Green
    } else {
        if ($tool.go_package) {
            Install-WithGo $tool.go_package $tool.name
        } elseif ($tool.choco_package) {
            Install-WithChoco $tool.choco_package $tool.name
        } elseif ($tool.pip_package) {
            Install-WithPip $tool.pip_package $tool.name
        }
    }
}

# ===============================================================================
# FASE 3 - FERRAMENTAS DE ANÁLISE DE VULNERABILIDADES
# ===============================================================================
Write-Host ""
Write-Host "📍 FASE 3 - FERRAMENTAS DE ANÁLISE DE VULNERABILIDADES" -ForegroundColor Magenta

$tools_fase3 = @(
    @{name="sqlmap"; pip_package="sqlmap"},
    @{name="dalfox"; go_package="github.com/hahwul/dalfox/v2@latest"},
    @{name="nuclei"; go_package="github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest"}
)

foreach ($tool in $tools_fase3) {
    if (Test-Command $tool.name) {
        Write-Host "✅ $($tool.name) já está instalado" -ForegroundColor Green
    } else {
        if ($tool.go_package) {
            Install-WithGo $tool.go_package $tool.name
        } elseif ($tool.pip_package) {
            Install-WithPip $tool.pip_package $tool.name
        }
    }
}

Write-Host ""
Write-Host "🔧 Configurando templates do Nuclei..." -ForegroundColor Yellow
try {
    nuclei -update-templates
    Write-Host "✅ Templates do Nuclei atualizados!" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Erro ao atualizar templates do Nuclei" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "🎉 INSTALAÇÃO CONCLUÍDA!" -ForegroundColor Green
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Verificação final das ferramentas:" -ForegroundColor Yellow

# Verificação final
$all_tools = @("subfinder", "amass", "assetfinder", "findomain", "httpx", "gobuster", "gowitness", 
               "feroxbuster", "katana", "gospider", "hakrawler", "paramspider", "arjun", "gf",
               "sqlmap", "dalfox", "nuclei")

$installed_count = 0
foreach ($tool in $all_tools) {
    if (Test-Command $tool) {
        Write-Host "✅ $tool" -ForegroundColor Green
        $installed_count++
    } else {
        Write-Host "❌ $tool" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 Resultado: $installed_count/$($all_tools.Count) ferramentas instaladas" -ForegroundColor Cyan
Write-Host ""

if ($installed_count -eq $all_tools.Count) {
    Write-Host "🎉 Todas as ferramentas foram instaladas com sucesso!" -ForegroundColor Green
    Write-Host "🚀 O Guardian IA está pronto para uso completo!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Algumas ferramentas não foram instaladas. Verifique os erros acima." -ForegroundColor Yellow
    Write-Host "💡 Você pode instalar manualmente as ferramentas faltantes." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Para testar o Guardian IA:" -ForegroundColor Cyan
Write-Host "python guardian_main.py --target exemplo.com --phases 1,2,3" -ForegroundColor White
Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan

pause
