@echo off
echo ===============================================================================
echo 🛡️  GUARDIAN IA - INSTALACAO RAPIDA DE DEPENDENCIAS
echo    "Protegendo o invisivel. Antecipando o inevitavel."
echo ===============================================================================

echo.
echo 📦 Instalando dependencias Python essenciais...
echo.

REM Instala apenas as dependências essenciais primeiro
pip install requests
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar requests
    pause
    exit /b 1
)

pip install beautifulsoup4
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar beautifulsoup4
    pause
    exit /b 1
)

pip install urllib3
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar urllib3
    pause
    exit /b 1
)

pip install jinja2
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar jinja2
    pause
    exit /b 1
)

echo.
echo ✅ Dependencias essenciais instaladas com sucesso!
echo.

echo 📦 Instalando dependencias opcionais...
pip install matplotlib seaborn pandas colorama --quiet

echo.
echo 🧪 Testando instalacao...
python -c "import requests, json, pathlib; print('✅ Imports basicos - OK')"
if %errorlevel% neq 0 (
    echo ❌ Erro nos imports basicos
    pause
    exit /b 1
)

echo.
echo ===============================================================================
echo 🎉 INSTALACAO CONCLUIDA COM SUCESSO!
echo ===============================================================================
echo.
echo Para executar o Guardian IA:
echo python guardian.py
echo.
echo Para setup completo (incluindo ferramentas Go):
echo python setup_guardian.py
echo.
echo ===============================================================================
pause
