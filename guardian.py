# guardian.py (Versão para Classe Avançada)

import os
import fase1_recon
# Os imports do utils e config não são mais necessários aqui, pois a classe gerencia isso
import fase2_enum
#import fase3_scan
# (Você pode manter os outros imports se as outras fases ainda os usarem)

def main():
    """Função principal que orquestra a execução de todas as fases."""
    
    # --- Configuração Inicial ---
    alvo = "hackerone.com" # Alvo principal
    diretorio_base_projeto = os.path.abspath(os.path.dirname(__file__))
    diretorio_saida_alvo = os.path.join(diretorio_base_projeto, "resultados", alvo)
    
    print(f"--- INICIANDO GUARDIAN IA PARA O ALVO: {alvo} ---")
    print(f"--- Os resultados serão salvos em: {diretorio_saida_alvo} ---")
    
    # --- Fase 1: Reconhecimento Estratégico ---
    # Define uma configuração personalizada para a Fase 1
    config_fase1 = {
        "timeout": 45,
        "threads": 40,
        "use_passive_only": False # Mude para True para um scan mais rápido e discreto
    }
    # Chama a nova função 'run' da Fase 1, que controla a classe
    fase1_recon.run(alvo, diretorio_saida_alvo, config_dict=config_fase1)
    
    # --- Fases Futuras ---
    # As chamadas para as fases 2 e 3 precisarão ser adaptadas para usar a saída da Fase 1
    print("\n--- INVOCANDO FASES SUBSEQUENTES ---\n")
    # fase2_enum.run(alvo, diretorio_saida_alvo)
    # fase3_scan.run(alvo, diretorio_saida_alvo)
    
    print(f"--- ANÁLISE COMPLETA CONCLUÍDA ---")

if __name__ == "__main__":
    main()
