# src/core/guardian.py - Classe principal do Guardian IA

import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from .config import get_config_for_target
from .logger import GuardianLogger
from ..phases.fase1_recon import ReconhecimentoEstrategico
from ..phases.fase2_enum import EnumeracaoAtiva
from ..phases.fase3_vulnerabilidades import AnalisadorVulnerabilidades
from ..phases.fase4_exploracao import ExploradorVulnerabilidades
from ..phases.fase5_pos_exploracao import AnalisadorPosExploracao
from ..phases.fase6_relatorio import GeradorRelatorioInteligente

class GuardianIA:
    """
    Classe principal do Guardian IA - Sistema de Pentest Automatizado
    
    Orquestra a execução das 6 fases da metodologia Guardian IA:
    1. Reconhecimento Estratégico e Coleta de Inteligência
    2. Enumeração Ativa e Mapeamento da Superfície de Ataque  
    3. Análise de Vulnerabilidades e Testes de Intrusão
    4. Exploração e Análise de Impacto
    5. Pós-Exploração e Análise de Movimento Lateral
    6. Relatório Inteligente e Plano de Ação
    """
    
    def __init__(self, target: str, custom_config: Optional[Dict] = None):
        """
        Inicializa o Guardian IA
        
        Args:
            target: Domínio alvo para análise
            custom_config: Configurações personalizadas (opcional)
        """
        self.target = target
        self.config = get_config_for_target(target)
        
        # Aplica configurações personalizadas se fornecidas
        if custom_config:
            self._merge_config(custom_config)
        
        # Cria diretório de saída
        self.output_dir = self.config["output_dir"]
        self.output_dir.mkdir(exist_ok=True, parents=True)
        
        # Configura logger principal
        self.logger = GuardianLogger.get_logger("guardian", target)
        
        # Resultados das fases
        self.phase_results = {}
        self.execution_start = None
        self.execution_end = None
        
        # Status de execução
        self.current_phase = None
        self.completed_phases = []
        self.failed_phases = []
    
    def _merge_config(self, custom_config: Dict):
        """Mescla configurações personalizadas com as padrão"""
        for key, value in custom_config.items():
            if key in self.config and isinstance(value, dict) and isinstance(self.config[key], dict):
                self.config[key].update(value)
            else:
                self.config[key] = value
    
    def _print_banner(self):
        """Exibe banner do Guardian IA"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                          🛡️  GUARDIAN IA                                    ║
║                "Protegendo o invisível. Antecipando o inevitável."           ║
║                                                                              ║
║                    Sistema de Pentest Automatizado v1.0                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"🎯 ALVO: {self.target}")
        print(f"📁 DIRETÓRIO DE SAÍDA: {self.output_dir}")
        print(f"⏰ INÍCIO: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        print("=" * 80)
    
    def execute_phase_1(self) -> Dict[str, Any]:
        """Executa Fase 1 - Reconhecimento Estratégico"""
        self.current_phase = "Fase 1 - Reconhecimento"
        
        try:
            GuardianLogger.log_phase_start(self.logger, "FASE 1", self.target)
            
            # Converte dicionário de volta para objeto ReconConfig
            from .config import ReconConfig
            recon_config = ReconConfig(**self.config["recon"]) if isinstance(self.config["recon"], dict) else self.config["recon"]

            recon = ReconhecimentoEstrategico(
                alvo=self.target,
                diretorio_saida=str(self.output_dir),
                config=recon_config
            )
            
            result = recon.executar_fase_completa()
            self.phase_results["fase1"] = result
            
            if result.get("sucesso"):
                self.completed_phases.append("fase1")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 1", 
                    result.get("duracao", "N/A"), True
                )
            else:
                self.failed_phases.append("fase1")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 1", 
                    result.get("duracao", "N/A"), False
                )
            
            return result
            
        except Exception as e:
            self.failed_phases.append("fase1")
            GuardianLogger.log_error_with_context(self.logger, e, "Fase 1")
            return {"sucesso": False, "erro": str(e)}
    
    def execute_phase_2(self) -> Dict[str, Any]:
        """Executa Fase 2 - Enumeração Ativa"""
        self.current_phase = "Fase 2 - Enumeração"
        
        try:
            GuardianLogger.log_phase_start(self.logger, "FASE 2", self.target)
            
            # Converte dicionário de volta para objeto EnumConfig
            from .config import EnumConfig
            enum_config = EnumConfig(**self.config["enum"]) if isinstance(self.config["enum"], dict) else self.config["enum"]

            enum = EnumeracaoAtiva(
                alvo=self.target,
                diretorio_saida=str(self.output_dir),
                config=enum_config
            )
            
            result = enum.executar_fase_completa()
            self.phase_results["fase2"] = result
            
            if result.get("sucesso"):
                self.completed_phases.append("fase2")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 2", 
                    result.get("duracao", "N/A"), True
                )
            else:
                self.failed_phases.append("fase2")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 2", 
                    result.get("duracao", "N/A"), False
                )
            
            return result
            
        except Exception as e:
            self.failed_phases.append("fase2")
            GuardianLogger.log_error_with_context(self.logger, e, "Fase 2")
            return {"sucesso": False, "erro": str(e)}
    
    def execute_phase_3(self) -> Dict[str, Any]:
        """Executa Fase 3 - Análise de Vulnerabilidades"""
        self.current_phase = "Fase 3 - Vulnerabilidades"
        
        try:
            GuardianLogger.log_phase_start(self.logger, "FASE 3", self.target)
            
            # Converte dicionário de volta para objeto VulnConfig
            from .config import VulnConfig
            vuln_config = VulnConfig(**self.config["vuln"]) if isinstance(self.config["vuln"], dict) else self.config["vuln"]

            vuln_analyzer = AnalisadorVulnerabilidades(
                alvo=self.target,
                diretorio_saida=str(self.output_dir),
                config=vuln_config
            )
            
            result = vuln_analyzer.executar_fase_completa()
            self.phase_results["fase3"] = result
            
            if result.get("sucesso"):
                self.completed_phases.append("fase3")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 3", 
                    result.get("duracao", "N/A"), True
                )
            else:
                self.failed_phases.append("fase3")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 3", 
                    result.get("duracao", "N/A"), False
                )
            
            return result
            
        except Exception as e:
            self.failed_phases.append("fase3")
            GuardianLogger.log_error_with_context(self.logger, e, "Fase 3")
            return {"sucesso": False, "erro": str(e)}
    
    def execute_phase_4(self) -> Dict[str, Any]:
        """Executa Fase 4 - Exploração e Análise de Impacto"""
        self.current_phase = "Fase 4 - Exploração"
        
        try:
            GuardianLogger.log_phase_start(self.logger, "FASE 4", self.target)
            
            # Converte dicionário de volta para objeto ExploracaoConfig
            from .config import ExploracaoConfig
            exploracao_config = ExploracaoConfig(**self.config["exploracao"]) if isinstance(self.config["exploracao"], dict) else self.config["exploracao"]

            explorador = ExploracaoImpacto(
                alvo=self.target,
                diretorio_saida=str(self.output_dir),
                config=exploracao_config
            )
            
            result = explorador.executar_fase_completa()
            self.phase_results["fase4"] = result
            
            if result.get("sucesso"):
                self.completed_phases.append("fase4")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 4", 
                    result.get("duracao", "N/A"), True
                )
            else:
                self.failed_phases.append("fase4")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 4", 
                    result.get("duracao", "N/A"), False
                )
            
            return result
            
        except Exception as e:
            self.failed_phases.append("fase4")
            GuardianLogger.log_error_with_context(self.logger, e, "Fase 4")
            return {"sucesso": False, "erro": str(e)}
    
    def execute_phase_5(self) -> Dict[str, Any]:
        """Executa Fase 5 - Pós-Exploração e Movimento Lateral"""
        self.current_phase = "Fase 5 - Pós-Exploração"
        
        try:
            GuardianLogger.log_phase_start(self.logger, "FASE 5", self.target)
            
            # Converte dicionário de volta para objeto PosExploracaoConfig
            from .config import PosExploracaoConfig
            pos_exploracao_config = PosExploracaoConfig(**self.config["pos_exploracao"]) if isinstance(self.config["pos_exploracao"], dict) else self.config["pos_exploracao"]

            pos_explorador = PosExploracaoAnalise(
                alvo=self.target,
                diretorio_saida=str(self.output_dir),
                config=pos_exploracao_config
            )
            
            result = pos_explorador.executar_fase_completa()
            self.phase_results["fase5"] = result
            
            if result.get("sucesso"):
                self.completed_phases.append("fase5")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 5", 
                    result.get("duracao", "N/A"), True
                )
            else:
                self.failed_phases.append("fase5")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 5", 
                    result.get("duracao", "N/A"), False
                )
            
            return result
            
        except Exception as e:
            self.failed_phases.append("fase5")
            GuardianLogger.log_error_with_context(self.logger, e, "Fase 5")
            return {"sucesso": False, "erro": str(e)}
    
    def execute_phase_6(self) -> Dict[str, Any]:
        """Executa Fase 6 - Relatório Inteligente"""
        self.current_phase = "Fase 6 - Relatório"
        
        try:
            GuardianLogger.log_phase_start(self.logger, "FASE 6", self.target)
            
            # Converte dicionário de volta para objeto RelatorioConfig
            from .config import RelatorioConfig
            relatorio_config = RelatorioConfig(**self.config["relatorio"]) if isinstance(self.config["relatorio"], dict) else self.config["relatorio"]

            relatorio_generator = RelatorioInteligente(
                alvo=self.target,
                diretorio_saida=str(self.output_dir),
                config=relatorio_config
            )
            
            result = relatorio_generator.executar_fase_completa()
            self.phase_results["fase6"] = result
            
            if result.get("sucesso"):
                self.completed_phases.append("fase6")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 6", 
                    result.get("duracao", "N/A"), True
                )
            else:
                self.failed_phases.append("fase6")
                GuardianLogger.log_phase_end(
                    self.logger, "FASE 6", 
                    result.get("duracao", "N/A"), False
                )
            
            return result
            
        except Exception as e:
            self.failed_phases.append("fase6")
            GuardianLogger.log_error_with_context(self.logger, e, "Fase 6")
            return {"sucesso": False, "erro": str(e)}
    
    def execute_all_phases(self) -> Dict[str, Any]:
        """Executa todas as 6 fases sequencialmente"""
        self.execution_start = datetime.now()
        
        try:
            self._print_banner()
            
            # Executa todas as fases
            phases = [
                ("🔍 FASE 1: RECONHECIMENTO ESTRATÉGICO", self.execute_phase_1),
                ("🎯 FASE 2: ENUMERAÇÃO ATIVA", self.execute_phase_2),
                ("🔬 FASE 3: ANÁLISE DE VULNERABILIDADES", self.execute_phase_3),
                ("💥 FASE 4: EXPLORAÇÃO E IMPACTO", self.execute_phase_4),
                ("🔄 FASE 5: PÓS-EXPLORAÇÃO", self.execute_phase_5),
                ("📊 FASE 6: RELATÓRIO INTELIGENTE", self.execute_phase_6)
            ]
            
            for phase_name, phase_func in phases:
                print(f"\n{phase_name}")
                print("-" * 60)
                
                result = phase_func()
                
                if result.get("sucesso"):
                    print(f"✅ {phase_name.split(':')[0]} concluída com sucesso!")
                else:
                    print(f"❌ {phase_name.split(':')[0]} falhou: {result.get('erro', 'Erro desconhecido')}")
                    
                    # Pergunta se deve continuar em caso de falha
                    if not self.config["global"].safe_mode:
                        continue_execution = input("Deseja continuar com as próximas fases? (y/n): ").lower()
                        if continue_execution != 'y':
                            break
            
            self.execution_end = datetime.now()
            return self._generate_final_summary()
            
        except KeyboardInterrupt:
            self.logger.warning("⚠️  Execução interrompida pelo usuário")
            return {"sucesso": False, "erro": "Interrompido pelo usuário"}
        except Exception as e:
            GuardianLogger.log_error_with_context(self.logger, e, "Execução geral")
            return {"sucesso": False, "erro": str(e)}
    
    def _generate_final_summary(self) -> Dict[str, Any]:
        """Gera resumo final da execução"""
        duration = self.execution_end - self.execution_start if self.execution_end else None
        
        summary = {
            "sucesso": len(self.failed_phases) == 0,
            "target": self.target,
            "execution_time": str(duration) if duration else "N/A",
            "completed_phases": len(self.completed_phases),
            "failed_phases": len(self.failed_phases),
            "phase_results": self.phase_results
        }
        
        # Exibe resumo final
        print("\n" + "="*80)
        print("🎉 GUARDIAN IA - EXECUÇÃO CONCLUÍDA!")
        print("="*80)
        print(f"🎯 Alvo: {self.target}")
        print(f"⏱️  Duração Total: {duration}")
        print(f"✅ Fases Concluídas: {len(self.completed_phases)}/6")
        print(f"❌ Fases Falhadas: {len(self.failed_phases)}")
        
        if self.failed_phases:
            print(f"⚠️  Fases com problemas: {', '.join(self.failed_phases)}")
        
        # Informações do relatório final se disponível
        if "fase6" in self.phase_results and self.phase_results["fase6"].get("sucesso"):
            fase6_result = self.phase_results["fase6"]
            print(f"\n📊 Score de Segurança: {fase6_result.get('score_seguranca', 'N/A')}/100")
            print(f"🎯 Nível de Risco: {fase6_result.get('nivel_risco', 'N/A')}")
            print(f"🔍 Vulnerabilidades: {fase6_result.get('vulnerabilidades_consolidadas', 'N/A')}")
        
        print(f"\n📁 Resultados salvos em: {self.output_dir}")
        print("\n🛡️  Guardian IA - Protegendo o invisível. Antecipando o inevitável.")
        print("="*80)
        
        return summary
    
    def get_status(self) -> Dict[str, Any]:
        """Retorna status atual da execução"""
        return {
            "target": self.target,
            "current_phase": self.current_phase,
            "completed_phases": self.completed_phases,
            "failed_phases": self.failed_phases,
            "execution_start": self.execution_start.isoformat() if self.execution_start else None,
            "is_running": self.current_phase is not None
        }
