#!/bin/bash
# ===============================================================================
# 🛡️  GUARDIAN IA - INSTALAÇÃO COMPLETA DE FERRAMENTAS DE PENTEST (Linux/WSL)
#    "Protegendo o invisível. Antecipando o inevitável."
# ===============================================================================

echo "==============================================================================="
echo "🛡️  GUARDIAN IA - INSTALAÇÃO DE FERRAMENTAS DE PENTEST"
echo "   Protegendo o invisível. Antecipando o inevitável."
echo "==============================================================================="
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Função para instalar via apt
install_apt() {
    local package=$1
    local name=$2
    echo -e "${YELLOW}📦 Instalando $name via apt...${NC}"
    if sudo apt-get install -y "$package"; then
        echo -e "${GREEN}✅ $name instalado com sucesso!${NC}"
        return 0
    else
        echo -e "${RED}❌ Erro ao instalar $name via apt${NC}"
        return 1
    fi
}

# Função para instalar via Go
install_go() {
    local package=$1
    local name=$2
    echo -e "${YELLOW}🔧 Instalando $name via Go...${NC}"
    if go install "$package"; then
        echo -e "${GREEN}✅ $name instalado com sucesso!${NC}"
        return 0
    else
        echo -e "${RED}❌ Erro ao instalar $name via Go${NC}"
        return 1
    fi
}

# Função para instalar via pip
install_pip() {
    local package=$1
    local name=$2
    echo -e "${YELLOW}🐍 Instalando $name via pip...${NC}"
    if pip3 install "$package"; then
        echo -e "${GREEN}✅ $name instalado com sucesso!${NC}"
        return 0
    else
        echo -e "${RED}❌ Erro ao instalar $name via pip${NC}"
        return 1
    fi
}

echo -e "${CYAN}🔍 Verificando pré-requisitos...${NC}"

# Atualiza repositórios
echo -e "${YELLOW}📦 Atualizando repositórios...${NC}"
sudo apt-get update

# Verifica se o Go está instalado
if ! command_exists go; then
    echo -e "${YELLOW}🔧 Instalando Go...${NC}"
    
    # Remove versões antigas
    sudo rm -rf /usr/local/go
    
    # Baixa e instala Go
    GO_VERSION="1.21.5"
    wget "https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz"
    sudo tar -C /usr/local -xzf "go${GO_VERSION}.linux-amd64.tar.gz"
    rm "go${GO_VERSION}.linux-amd64.tar.gz"
    
    # Adiciona ao PATH
    echo 'export PATH=$PATH:/usr/local/go/bin:$HOME/go/bin' >> ~/.bashrc
    export PATH=$PATH:/usr/local/go/bin:$HOME/go/bin
    
    if command_exists go; then
        echo -e "${GREEN}✅ Go instalado!${NC}"
    else
        echo -e "${RED}❌ Falha ao instalar Go${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Go já está instalado${NC}"
fi

# Verifica se o Python/pip está instalado
if ! command_exists pip3; then
    echo -e "${YELLOW}🐍 Instalando Python3 e pip...${NC}"
    install_apt "python3-pip" "Python3 e pip"
fi

# Instala dependências básicas
echo -e "${YELLOW}📦 Instalando dependências básicas...${NC}"
sudo apt-get install -y curl wget git build-essential

echo ""
echo -e "${CYAN}🚀 Iniciando instalação das ferramentas de pentest...${NC}"
echo ""

# ===============================================================================
# FASE 1 - FERRAMENTAS DE RECONHECIMENTO
# ===============================================================================
echo -e "${MAGENTA}📍 FASE 1 - FERRAMENTAS DE RECONHECIMENTO${NC}"

# Subfinder
if command_exists subfinder; then
    echo -e "${GREEN}✅ subfinder já está instalado${NC}"
else
    install_go "github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest" "subfinder"
fi

# Amass
if command_exists amass; then
    echo -e "${GREEN}✅ amass já está instalado${NC}"
else
    install_go "github.com/owasp-amass/amass/v4/...@master" "amass"
fi

# Assetfinder
if command_exists assetfinder; then
    echo -e "${GREEN}✅ assetfinder já está instalado${NC}"
else
    install_go "github.com/tomnomnom/assetfinder@latest" "assetfinder"
fi

# Findomain
if command_exists findomain; then
    echo -e "${GREEN}✅ findomain já está instalado${NC}"
else
    echo -e "${YELLOW}🔧 Instalando findomain...${NC}"
    wget https://github.com/Findomain/Findomain/releases/latest/download/findomain-linux
    chmod +x findomain-linux
    sudo mv findomain-linux /usr/local/bin/findomain
    echo -e "${GREEN}✅ findomain instalado!${NC}"
fi

# HTTPx
if command_exists httpx; then
    echo -e "${GREEN}✅ httpx já está instalado${NC}"
else
    install_go "github.com/projectdiscovery/httpx/cmd/httpx@latest" "httpx"
fi

# Gobuster
if command_exists gobuster; then
    echo -e "${GREEN}✅ gobuster já está instalado${NC}"
else
    install_go "github.com/OJ/gobuster/v3@latest" "gobuster"
fi

# Gowitness
if command_exists gowitness; then
    echo -e "${GREEN}✅ gowitness já está instalado${NC}"
else
    install_go "github.com/sensepost/gowitness@latest" "gowitness"
fi

# ===============================================================================
# FASE 2 - FERRAMENTAS DE ENUMERAÇÃO
# ===============================================================================
echo ""
echo -e "${MAGENTA}📍 FASE 2 - FERRAMENTAS DE ENUMERAÇÃO${NC}"

# Feroxbuster
if command_exists feroxbuster; then
    echo -e "${GREEN}✅ feroxbuster já está instalado${NC}"
else
    echo -e "${YELLOW}🔧 Instalando feroxbuster...${NC}"
    wget -O feroxbuster.deb https://github.com/epi052/feroxbuster/releases/latest/download/feroxbuster_amd64.deb
    sudo dpkg -i feroxbuster.deb
    rm feroxbuster.deb
    echo -e "${GREEN}✅ feroxbuster instalado!${NC}"
fi

# Katana
if command_exists katana; then
    echo -e "${GREEN}✅ katana já está instalado${NC}"
else
    install_go "github.com/projectdiscovery/katana/cmd/katana@latest" "katana"
fi

# Gospider
if command_exists gospider; then
    echo -e "${GREEN}✅ gospider já está instalado${NC}"
else
    install_go "github.com/jaeles-project/gospider@latest" "gospider"
fi

# Hakrawler
if command_exists hakrawler; then
    echo -e "${GREEN}✅ hakrawler já está instalado${NC}"
else
    install_go "github.com/hakluke/hakrawler@latest" "hakrawler"
fi

# ParamSpider
if command_exists paramspider; then
    echo -e "${GREEN}✅ paramspider já está instalado${NC}"
else
    install_pip "paramspider" "paramspider"
fi

# Arjun
if command_exists arjun; then
    echo -e "${GREEN}✅ arjun já está instalado${NC}"
else
    install_pip "arjun" "arjun"
fi

# GF
if command_exists gf; then
    echo -e "${GREEN}✅ gf já está instalado${NC}"
else
    install_go "github.com/tomnomnom/gf@latest" "gf"
fi

# ===============================================================================
# FASE 3 - FERRAMENTAS DE ANÁLISE DE VULNERABILIDADES
# ===============================================================================
echo ""
echo -e "${MAGENTA}📍 FASE 3 - FERRAMENTAS DE ANÁLISE DE VULNERABILIDADES${NC}"

# SQLMap
if command_exists sqlmap; then
    echo -e "${GREEN}✅ sqlmap já está instalado${NC}"
else
    install_pip "sqlmap" "sqlmap"
fi

# Dalfox
if command_exists dalfox; then
    echo -e "${GREEN}✅ dalfox já está instalado${NC}"
else
    install_go "github.com/hahwul/dalfox/v2@latest" "dalfox"
fi

# Nuclei
if command_exists nuclei; then
    echo -e "${GREEN}✅ nuclei já está instalado${NC}"
else
    install_go "github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest" "nuclei"
fi

# Atualiza templates do Nuclei
echo ""
echo -e "${YELLOW}🔧 Configurando templates do Nuclei...${NC}"
if command_exists nuclei; then
    nuclei -update-templates
    echo -e "${GREEN}✅ Templates do Nuclei atualizados!${NC}"
fi

# ===============================================================================
# WORDLISTS
# ===============================================================================
echo ""
echo -e "${MAGENTA}📍 INSTALANDO WORDLISTS${NC}"

# SecLists
if [ ! -d "/usr/share/wordlists/SecLists" ]; then
    echo -e "${YELLOW}📝 Instalando SecLists...${NC}"
    sudo mkdir -p /usr/share/wordlists
    sudo git clone https://github.com/danielmiessler/SecLists.git /usr/share/wordlists/SecLists
    echo -e "${GREEN}✅ SecLists instalado!${NC}"
else
    echo -e "${GREEN}✅ SecLists já está instalado${NC}"
fi

echo ""
echo "==============================================================================="
echo -e "${GREEN}🎉 INSTALAÇÃO CONCLUÍDA!${NC}"
echo "==============================================================================="
echo ""
echo -e "${YELLOW}📋 Verificação final das ferramentas:${NC}"

# Verificação final
tools=("subfinder" "amass" "assetfinder" "findomain" "httpx" "gobuster" "gowitness" 
       "feroxbuster" "katana" "gospider" "hakrawler" "paramspider" "arjun" "gf"
       "sqlmap" "dalfox" "nuclei")

installed_count=0
total_tools=${#tools[@]}

for tool in "${tools[@]}"; do
    if command_exists "$tool"; then
        echo -e "${GREEN}✅ $tool${NC}"
        ((installed_count++))
    else
        echo -e "${RED}❌ $tool${NC}"
    fi
done

echo ""
echo -e "${CYAN}📊 Resultado: $installed_count/$total_tools ferramentas instaladas${NC}"
echo ""

if [ $installed_count -eq $total_tools ]; then
    echo -e "${GREEN}🎉 Todas as ferramentas foram instaladas com sucesso!${NC}"
    echo -e "${GREEN}🚀 O Guardian IA está pronto para uso completo!${NC}"
else
    echo -e "${YELLOW}⚠️  Algumas ferramentas não foram instaladas. Verifique os erros acima.${NC}"
    echo -e "${YELLOW}💡 Você pode instalar manualmente as ferramentas faltantes.${NC}"
fi

echo ""
echo -e "${CYAN}Para testar o Guardian IA:${NC}"
echo -e "${NC}python3 guardian_main.py --target exemplo.com --phases 1,2,3${NC}"
echo ""
echo "==============================================================================="
