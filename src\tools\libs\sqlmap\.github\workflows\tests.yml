on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        python-version: [ 'pypy-2.7', '3.13' ]
        exclude:
          - os: macos-latest
            python-version: 'pypy-2.7'
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
      - name: Basic import test
        run: python -c "import sqlmap; import sqlmapapi"
      - name: Smoke test
        run: python sqlmap.py --smoke
      - name: Vuln test
        run: python sqlmap.py --vuln
