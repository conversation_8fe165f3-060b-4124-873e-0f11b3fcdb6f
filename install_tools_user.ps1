# GUARDIAN IA - Instalacao de Ferramentas (Usuario Normal)
# Instala ferramentas que nao precisam de privilegios administrativos

Write-Host "GUARDIAN IA - Instalacao de Ferramentas (Usuario)" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Funcao para verificar se comando existe
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Verifica se Go esta instalado
if (-not (Test-Command "go")) {
    Write-Host "ERRO: Go nao esta instalado!" -ForegroundColor Red
    Write-Host "Por favor, instale Go primeiro:" -ForegroundColor Yellow
    Write-Host "1. Va para https://golang.org/dl/" -ForegroundColor White
    Write-Host "2. Baixe e instale Go para Windows" -ForegroundColor White
    Write-Host "3. Reinicie o PowerShell e execute este script novamente" -ForegroundColor White
    pause
    exit 1
}

# Verifica se Python/pip esta instalado
if (-not (Test-Command "pip")) {
    Write-Host "ERRO: Python/pip nao esta instalado!" -ForegroundColor Red
    Write-Host "Por favor, instale Python primeiro:" -ForegroundColor Yellow
    Write-Host "1. Va para https://python.org/downloads/" -ForegroundColor White
    Write-Host "2. Baixe e instale Python para Windows" -ForegroundColor White
    Write-Host "3. Marque a opcao 'Add Python to PATH'" -ForegroundColor White
    Write-Host "4. Reinicie o PowerShell e execute este script novamente" -ForegroundColor White
    pause
    exit 1
}

Write-Host "Instalando ferramentas Go..." -ForegroundColor Green

# Lista de ferramentas Go
$goTools = @(
    @{name="subfinder"; package="github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest"},
    @{name="amass"; package="github.com/owasp-amass/amass/v4/...@master"},
    @{name="assetfinder"; package="github.com/tomnomnom/assetfinder@latest"},
    @{name="httpx"; package="github.com/projectdiscovery/httpx/cmd/httpx@latest"},
    @{name="gobuster"; package="github.com/OJ/gobuster/v3@latest"},
    @{name="gowitness"; package="github.com/sensepost/gowitness@latest"},
    @{name="katana"; package="github.com/projectdiscovery/katana/cmd/katana@latest"},
    @{name="gospider"; package="github.com/jaeles-project/gospider@latest"},
    @{name="hakrawler"; package="github.com/hakluke/hakrawler@latest"},
    @{name="gf"; package="github.com/tomnomnom/gf@latest"},
    @{name="dalfox"; package="github.com/hahwul/dalfox/v2@latest"},
    @{name="nuclei"; package="github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest"}
)

foreach ($tool in $goTools) {
    if (Test-Command $tool.name) {
        Write-Host "OK: $($tool.name) ja esta instalado" -ForegroundColor Green
    } else {
        Write-Host "Instalando $($tool.name)..." -ForegroundColor Yellow
        try {
            go install $tool.package
            if (Test-Command $tool.name) {
                Write-Host "SUCESSO: $($tool.name) instalado!" -ForegroundColor Green
            } else {
                Write-Host "AVISO: $($tool.name) pode ter sido instalado mas nao esta no PATH" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "ERRO: Falha ao instalar $($tool.name)" -ForegroundColor Red
        }
    }
}

Write-Host "Instalando ferramentas Python..." -ForegroundColor Green

# Lista de ferramentas Python
$pythonTools = @(
    @{name="sqlmap"; package="sqlmap"},
    @{name="paramspider"; package="paramspider"},
    @{name="arjun"; package="arjun"}
)

foreach ($tool in $pythonTools) {
    if (Test-Command $tool.name) {
        Write-Host "OK: $($tool.name) ja esta instalado" -ForegroundColor Green
    } else {
        Write-Host "Instalando $($tool.name)..." -ForegroundColor Yellow
        try {
            pip install $tool.package
            Write-Host "SUCESSO: $($tool.name) instalado!" -ForegroundColor Green
        } catch {
            Write-Host "ERRO: Falha ao instalar $($tool.name)" -ForegroundColor Red
        }
    }
}

# Instala findomain manualmente
if (-not (Test-Command "findomain")) {
    Write-Host "Instalando findomain..." -ForegroundColor Yellow
    try {
        $findomain_url = "https://github.com/Findomain/Findomain/releases/latest/download/findomain-windows.exe"
        $findomain_path = "$env:USERPROFILE\go\bin\findomain.exe"
        
        # Cria diretorio se nao existir
        $go_bin_dir = "$env:USERPROFILE\go\bin"
        if (-not (Test-Path $go_bin_dir)) {
            New-Item -ItemType Directory -Path $go_bin_dir -Force
        }
        
        # Baixa findomain
        Invoke-WebRequest -Uri $findomain_url -OutFile $findomain_path
        
        if (Test-Path $findomain_path) {
            Write-Host "SUCESSO: findomain instalado!" -ForegroundColor Green
        }
    } catch {
        Write-Host "ERRO: Falha ao instalar findomain" -ForegroundColor Red
    }
} else {
    Write-Host "OK: findomain ja esta instalado" -ForegroundColor Green
}

# Atualiza templates do Nuclei
if (Test-Command "nuclei") {
    Write-Host "Atualizando templates do Nuclei..." -ForegroundColor Yellow
    try {
        nuclei -update-templates
        Write-Host "SUCESSO: Templates do Nuclei atualizados!" -ForegroundColor Green
    } catch {
        Write-Host "AVISO: Erro ao atualizar templates do Nuclei" -ForegroundColor Yellow
    }
}

Write-Host "Verificacao final:" -ForegroundColor Cyan

# Lista de todas as ferramentas para verificar
$allTools = @(
    "subfinder", "amass", "assetfinder", "httpx", "gobuster", "gowitness",
    "katana", "gospider", "hakrawler", "gf", "dalfox", "nuclei",
    "sqlmap", "paramspider", "arjun", "findomain"
)

$installedCount = 0
foreach ($tool in $allTools) {
    if (Test-Command $tool) {
        Write-Host "OK: $tool" -ForegroundColor Green
        $installedCount++
    } else {
        Write-Host "FALTANDO: $tool" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Resultado: $installedCount/$($allTools.Count) ferramentas instaladas" -ForegroundColor Cyan

if ($installedCount -eq $allTools.Count) {
    Write-Host "SUCESSO! Todas as ferramentas foram instaladas!" -ForegroundColor Green
    Write-Host "O Guardian IA esta pronto para uso!" -ForegroundColor Green
} elseif ($installedCount -gt ($allTools.Count * 0.7)) {
    Write-Host "BOM! A maioria das ferramentas foi instalada." -ForegroundColor Yellow
    Write-Host "O Guardian IA pode funcionar com limitacoes." -ForegroundColor Yellow
} else {
    Write-Host "AVISO: Poucas ferramentas foram instaladas." -ForegroundColor Red
    Write-Host "Verifique os pre-requisitos (Go e Python)." -ForegroundColor Red
}

Write-Host ""
Write-Host "FERRAMENTAS QUE PRECISAM DE INSTALACAO MANUAL:" -ForegroundColor Yellow
Write-Host "- feroxbuster: https://github.com/epi052/feroxbuster/releases" -ForegroundColor White
Write-Host ""
Write-Host "Para testar o Guardian IA:" -ForegroundColor Cyan
Write-Host "python guardian_main.py --target exemplo.com --phases 1" -ForegroundColor White
Write-Host ""

pause
