# src/phases/fase3_vulnerabilidades.py - Fase 3: Análise de Vulnerabilidades e Testes de Intrusão

import os
import json
import subprocess
import re
import time
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse, parse_qs

# Imports da nova estrutura
from ..core.logger import get_logger
from ..core.config import DEFAULT_VULN_CONFIG
from ..utils.file_utils import save_json, load_json, save_csv
from ..utils.command_utils import CommandExecutor, ToolRunner

@dataclass
class VulnConfig:
    """Configurações para análise de vulnerabilidades"""
    timeout: int = 60
    threads: int = 20
    max_targets_per_engine: int = 50
    sqlmap_risk: int = 1
    sqlmap_level: int = 1
    nuclei_severity: List[str] = None
    safe_mode: bool = True  # Modo seguro para evitar danos
    
    def __post_init__(self):
        if self.nuclei_severity is None:
            self.nuclei_severity = ["critical", "high", "medium"]

@dataclass
class VulnerabilityInfo:
    """Informações detalhadas de uma vulnerabilidade"""
    target_url: str
    vulnerability_type: str
    severity: str
    confidence: str
    description: str
    proof_of_concept: Optional[str] = None
    impact_analysis: Optional[str] = None
    remediation: Optional[str] = None
    cvss_score: Optional[float] = None
    discovered_by: str = "unknown"
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class TargetInfo:
    """Informações de um alvo para teste"""
    url: str
    target_type: str  # URL_PARAM, FORM, API_ENDPOINT, etc.
    parameters: List[str]
    potential_vectors: List[str]
    method: str = "GET"
    
class AnalisadorVulnerabilidades:
    """Classe principal para análise de vulnerabilidades"""
    
    def __init__(self, alvo: str, diretorio_saida: str, config: VulnConfig = None):
        self.alvo = alvo
        self.diretorio_saida = Path(diretorio_saida)
        self.config = config or VulnConfig()
        self.vulnerabilidades_encontradas = []
        self._criar_estrutura_diretorios()
        self.logger = self._configurar_logging()
        self.targets = self._carregar_targets()
    
    def _configurar_logging(self):
        """Configura logging detalhado"""
        self.logger = get_logger(f"vuln_{self.alvo}")
        return self.logger
    
    def _criar_estrutura_diretorios(self):
        """Cria estrutura de diretórios para a Fase 3"""
        subdirs = [
            "sql_injection", "xss", "command_injection", "lfi_ssti", 
            "broken_auth", "idor", "nuclei_scans", "relatorios_fase3"
        ]
        for subdir in subdirs:
            (self.diretorio_saida / subdir).mkdir(exist_ok=True, parents=True)
    
    def _carregar_targets(self) -> List[TargetInfo]:
        """Carrega targets da Fase 2 ou cria targets básicos da Fase 1"""
        targets = []
        
        # Tenta carregar targets da Fase 2 primeiro
        arquivo_targets_fase2 = self.diretorio_saida / "relatorios_fase2" / "targets.json"
        if arquivo_targets_fase2.exists():
            try:
                with open(arquivo_targets_fase2, 'r') as f:
                    data = json.load(f)
                    for item in data.get('targets', []):
                        target = TargetInfo(**item)
                        targets.append(target)
                self.logger.info(f"Carregados {len(targets)} targets da Fase 2")
                return targets
            except Exception as e:
                self.logger.warning(f"Erro ao carregar targets da Fase 2: {e}")
        
        # Fallback: cria targets básicos da Fase 1
        arquivo_httpx = self.diretorio_saida / "subdominios" / "ativos_detalhados.json"
        if arquivo_httpx.exists():
            try:
                with open(arquivo_httpx, 'r') as f:
                    for linha in f:
                        if linha.strip():
                            data = json.loads(linha)
                            url = data.get('url', '')
                            if url and data.get('status_code', 0) in [200, 301, 302]:
                                target = TargetInfo(
                                    url=url,
                                    target_type="URL_BASIC",
                                    parameters=[],
                                    potential_vectors=["SQLi", "XSS", "Nuclei"]
                                )
                                targets.append(target)
                self.logger.info(f"Criados {len(targets)} targets básicos da Fase 1")
            except Exception as e:
                self.logger.error(f"Erro ao carregar URLs da Fase 1: {e}")
        
        return targets[:self.config.max_targets_per_engine]  # Limita quantidade
    
    def _executar_ferramenta_segura(self, comando: List[str], arquivo_saida: str, timeout_multiplier: int = 1) -> bool:
        """Executa comando com tratamento de erro e timeout personalizado"""
        try:
            timeout_total = self.config.timeout * timeout_multiplier
            self.logger.info(f"Executando: {' '.join(comando[:3])}... (timeout: {timeout_total}s)")
            
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                processo = subprocess.run(
                    comando, 
                    stdout=f, 
                    stderr=subprocess.PIPE,
                    timeout=timeout_total, 
                    text=True, 
                    encoding='utf-8'
                )
            
            if processo.returncode == 0:
                self.logger.info(f"✅ Sucesso: {comando[0]}")
                return True
            else:
                if processo.stderr:
                    self.logger.warning(f"⚠️ Aviso em {comando[0]}: {processo.stderr.strip()[:200]}")
                return False
                
        except FileNotFoundError:
            self.logger.error(f"❌ Ferramenta {comando[0]} não encontrada no PATH")
            return False
        except subprocess.TimeoutExpired:
            self.logger.error(f"⏰ Timeout em {comando[0]} após {timeout_total}s")
            return False
        except Exception as e:
            self.logger.error(f"❌ Erro inesperado em {comando[0]}: {str(e)}")
            return False

    def sql_injection_engine(self) -> List[VulnerabilityInfo]:
        """Engine de SQL Injection usando SQLMap"""
        self.logger.info("🔍 Iniciando SQL Injection Engine...")
        vulnerabilidades = []
        
        # Filtra targets com potencial para SQLi
        targets_sqli = [t for t in self.targets if "SQLi" in t.potential_vectors]
        
        for target in targets_sqli[:10]:  # Limita para evitar scans muito longos
            arquivo_saida = self.diretorio_saida / "sql_injection" / f"sqlmap_{hash(target.url) % 10000}.txt"
            
            comando = [
                "sqlmap",
                "-u", target.url,
                "--batch",
                f"--risk={self.config.sqlmap_risk}",
                f"--level={self.config.sqlmap_level}",
                "--random-agent",
                "--timeout=30",
                "--retries=1"
            ]
            
            if self.config.safe_mode:
                comando.extend(["--no-cast", "--skip-urlencode"])
            
            if self._executar_ferramenta_segura(comando, str(arquivo_saida), timeout_multiplier=2):
                vuln_info = self._processar_resultado_sqlmap(str(arquivo_saida), target.url)
                if vuln_info:
                    vulnerabilidades.extend(vuln_info)
        
        self.logger.info(f"✅ SQL Injection Engine: {len(vulnerabilidades)} vulnerabilidades encontradas")
        return vulnerabilidades

    def _processar_resultado_sqlmap(self, arquivo: str, url: str) -> List[VulnerabilityInfo]:
        """Processa resultado do SQLMap"""
        vulnerabilidades = []
        try:
            with open(arquivo, 'r', encoding='utf-8', errors='ignore') as f:
                conteudo = f.read()
                
            # Busca por indicadores de SQL Injection
            if any(indicador in conteudo.lower() for indicador in [
                "parameter", "injectable", "payload", "sqlmap identified"
            ]):
                vuln = VulnerabilityInfo(
                    target_url=url,
                    vulnerability_type="SQL Injection",
                    severity="High",
                    confidence="Medium",
                    description="Possível vulnerabilidade de SQL Injection detectada",
                    proof_of_concept="Verificar logs do SQLMap para detalhes",
                    discovered_by="SQLMap"
                )
                vulnerabilidades.append(vuln)
                
        except Exception as e:
            self.logger.error(f"Erro ao processar resultado SQLMap: {e}")
            
        return vulnerabilidades

    def xss_engine(self) -> List[VulnerabilityInfo]:
        """Engine de XSS usando Dalfox"""
        self.logger.info("🔍 Iniciando XSS Engine...")
        vulnerabilidades = []
        
        targets_xss = [t for t in self.targets if "XSS" in t.potential_vectors]
        
        for target in targets_xss[:15]:  # Limita quantidade
            arquivo_saida = self.diretorio_saida / "xss" / f"dalfox_{hash(target.url) % 10000}.txt"
            
            comando = [
                "dalfox",
                "url", target.url,
                "--silence",
                "--no-color",
                "--timeout", "30"
            ]
            
            if self._executar_ferramenta_segura(comando, str(arquivo_saida)):
                vuln_info = self._processar_resultado_dalfox(str(arquivo_saida), target.url)
                if vuln_info:
                    vulnerabilidades.extend(vuln_info)
        
        self.logger.info(f"✅ XSS Engine: {len(vulnerabilidades)} vulnerabilidades encontradas")
        return vulnerabilidades

    def _processar_resultado_dalfox(self, arquivo: str, url: str) -> List[VulnerabilityInfo]:
        """Processa resultado do Dalfox"""
        vulnerabilidades = []
        try:
            with open(arquivo, 'r', encoding='utf-8', errors='ignore') as f:
                conteudo = f.read()
                
            # Busca por indicadores de XSS
            if any(indicador in conteudo.lower() for indicador in [
                "xss", "reflected", "stored", "vulnerable"
            ]):
                vuln = VulnerabilityInfo(
                    target_url=url,
                    vulnerability_type="Cross-Site Scripting (XSS)",
                    severity="Medium",
                    confidence="Medium",
                    description="Possível vulnerabilidade de XSS detectada",
                    proof_of_concept="Verificar logs do Dalfox para detalhes",
                    discovered_by="Dalfox"
                )
                vulnerabilidades.append(vuln)
                
        except Exception as e:
            self.logger.error(f"Erro ao processar resultado Dalfox: {e}")
            
        return vulnerabilidades

    def nuclei_engine(self) -> List[VulnerabilityInfo]:
        """Engine de análise de componentes usando Nuclei"""
        self.logger.info("🔍 Iniciando Nuclei Engine...")
        vulnerabilidades = []
        
        # Cria lista de URLs para o Nuclei
        urls_file = self.diretorio_saida / "nuclei_scans" / "targets.txt"
        with open(urls_file, 'w') as f:
            for target in self.targets:
                f.write(f"{target.url}\n")
        
        arquivo_saida = self.diretorio_saida / "nuclei_scans" / "nuclei_results.json"
        
        comando = [
            "nuclei",
            "-l", str(urls_file),
            "-severity", ",".join(self.config.nuclei_severity),
            "-json",
            "-silent",
            "-timeout", "30",
            "-retries", "1"
        ]
        
        if self._executar_ferramenta_segura(comando, str(arquivo_saida), timeout_multiplier=3):
            vulnerabilidades = self._processar_resultado_nuclei(str(arquivo_saida))
        
        self.logger.info(f"✅ Nuclei Engine: {len(vulnerabilidades)} vulnerabilidades encontradas")
        return vulnerabilidades

    def _processar_resultado_nuclei(self, arquivo: str) -> List[VulnerabilityInfo]:
        """Processa resultado do Nuclei"""
        vulnerabilidades = []
        try:
            with open(arquivo, 'r', encoding='utf-8') as f:
                for linha in f:
                    if linha.strip():
                        data = json.loads(linha)
                        
                        vuln = VulnerabilityInfo(
                            target_url=data.get('matched-at', ''),
                            vulnerability_type=data.get('info', {}).get('name', 'Unknown'),
                            severity=data.get('info', {}).get('severity', 'info').title(),
                            confidence="High",
                            description=data.get('info', {}).get('description', ''),
                            proof_of_concept=data.get('matched-at', ''),
                            discovered_by="Nuclei"
                        )
                        vulnerabilidades.append(vuln)
                        
        except Exception as e:
            self.logger.error(f"Erro ao processar resultado Nuclei: {e}")
            
        return vulnerabilidades

    def executar_fase_completa(self) -> Dict:
        """Executa o fluxo completo da Fase 3"""
        inicio = datetime.now()
        try:
            self.logger.info(f"🚀 INICIANDO FASE 3: ANÁLISE DE VULNERABILIDADES PARA {self.alvo}")
            self.logger.info(f"📊 Targets carregados: {len(self.targets)}")
            
            # Executa engines de vulnerabilidade
            sqli_vulns = self.sql_injection_engine()
            xss_vulns = self.xss_engine()
            nuclei_vulns = self.nuclei_engine()
            
            # Consolida todas as vulnerabilidades
            self.vulnerabilidades_encontradas = sqli_vulns + xss_vulns + nuclei_vulns
            
            # Gera relatório
            self._gerar_relatorio_fase3()
            
            duracao = datetime.now() - inicio
            self.logger.info("="*60)
            self.logger.info("🎉 FASE 3 CONCLUÍDA COM SUCESSO!")
            self.logger.info(f"🔍 Total de vulnerabilidades encontradas: {len(self.vulnerabilidades_encontradas)}")
            self.logger.info(f"⏱️  Duração total: {duracao}")
            
            return {
                'sucesso': True,
                'vulnerabilidades_encontradas': len(self.vulnerabilidades_encontradas),
                'duracao': str(duracao)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erro crítico na Fase 3: {str(e)}", exc_info=True)
            return {'sucesso': False, 'erro': str(e)}

    def _gerar_relatorio_fase3(self):
        """Gera relatório detalhado da Fase 3"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Relatório JSON
        relatorio_json = {
            "alvo": self.alvo,
            "timestamp": timestamp,
            "total_vulnerabilidades": len(self.vulnerabilidades_encontradas),
            "vulnerabilidades_por_tipo": self._agrupar_por_tipo(),
            "vulnerabilidades_por_severidade": self._agrupar_por_severidade(),
            "vulnerabilidades": [asdict(v) for v in self.vulnerabilidades_encontradas]
        }
        
        arquivo_json = self.diretorio_saida / "relatorios_fase3" / f"vulnerabilidades_{timestamp}.json"
        with open(arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(relatorio_json, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📋 Relatório salvo: {arquivo_json}")

    def _agrupar_por_tipo(self) -> Dict[str, int]:
        """Agrupa vulnerabilidades por tipo"""
        tipos = {}
        for vuln in self.vulnerabilidades_encontradas:
            tipos[vuln.vulnerability_type] = tipos.get(vuln.vulnerability_type, 0) + 1
        return tipos

    def _agrupar_por_severidade(self) -> Dict[str, int]:
        """Agrupa vulnerabilidades por severidade"""
        severidades = {}
        for vuln in self.vulnerabilidades_encontradas:
            severidades[vuln.severity] = severidades.get(vuln.severity, 0) + 1
        return severidades

def run(alvo: str, diretorio_saida: str, config_dict: Dict = None):
    """Interface principal para executar a Fase 3"""
    config = VulnConfig(**config_dict) if config_dict else VulnConfig()
    analisador = AnalisadorVulnerabilidades(alvo, diretorio_saida, config)
    return analisador.executar_fase_completa()
