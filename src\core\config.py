# src/core/config.py - Configurações centralizadas do Guardian IA

import os
from pathlib import Path
from dataclasses import dataclass, field
from typing import List, Dict, Optional

# Diretórios base
PROJECT_ROOT = Path(__file__).parent.parent.parent
SRC_DIR = PROJECT_ROOT / "src"
RESULTS_DIR = PROJECT_ROOT / "results"
LOGS_DIR = PROJECT_ROOT / "logs"
WORDLISTS_DIR = PROJECT_ROOT / "wordlists"
CONFIG_DIR = PROJECT_ROOT / "config"

@dataclass
class GlobalConfig:
    """Configurações globais do Guardian IA"""
    # Diretórios
    project_root: Path = PROJECT_ROOT
    results_dir: Path = RESULTS_DIR
    logs_dir: Path = LOGS_DIR
    wordlists_dir: Path = WORDLISTS_DIR
    
    # Configurações gerais
    safe_mode: bool = True
    max_threads: int = 50
    default_timeout: int = 60
    user_agent: str = "Guardian-IA/1.0 (Security Scanner)"
    
    # Rate limiting
    requests_per_second: int = 10
    delay_between_requests: float = 0.1
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Ferramentas externas
    tools_timeout: int = 300  # 5 minutos
    
    def __post_init__(self):
        """Cria diretórios necessários"""
        for directory in [self.results_dir, self.logs_dir, self.wordlists_dir]:
            directory.mkdir(exist_ok=True, parents=True)

@dataclass
class ReconConfig:
    """Configurações para Fase 1 - Reconhecimento"""
    timeout: int = 60
    max_subdominios: int = 1000
    verificar_httpx: bool = True
    incluir_tecnologias: bool = True
    use_passive_only: bool = False
    threads: int = 40
    
    # Ferramentas
    subfinder_config: Dict = field(default_factory=lambda: {
        "sources": ["all"],
        "timeout": 30,
        "silent": True
    })
    
    httpx_config: Dict = field(default_factory=lambda: {
        "timeout": 10,
        "follow_redirects": True,
        "tech_detect": True
    })

@dataclass
class EnumConfig:
    """Configurações para Fase 2 - Enumeração"""
    timeout: int = 45
    max_diretorios: int = 500
    incluir_parametros: bool = True
    wordlist_personalizada: bool = True
    threads: int = 30
    
    # Gobuster
    gobuster_config: Dict = field(default_factory=lambda: {
        "extensions": ["php", "html", "js", "txt", "json", "xml"],
        "status_codes": "200,204,301,302,307,401,403",
        "timeout": "10s"
    })

@dataclass
class VulnConfig:
    """Configurações para Fase 3 - Vulnerabilidades"""
    timeout: int = 60
    sqlmap_risk: int = 1
    safe_mode: bool = True
    incluir_nuclei: bool = True
    max_urls_per_tool: int = 100
    
    # SQLMap
    sqlmap_config: Dict = field(default_factory=lambda: {
        "level": 1,
        "risk": 1,
        "timeout": 30,
        "threads": 5,
        "batch": True
    })
    
    # Nuclei
    nuclei_config: Dict = field(default_factory=lambda: {
        "severity": ["critical", "high", "medium"],
        "timeout": 30,
        "rate_limit": 150
    })
    
    # Dalfox (XSS)
    dalfox_config: Dict = field(default_factory=lambda: {
        "timeout": 30,
        "worker": 10,
        "delay": 1
    })

@dataclass
class ExploracaoConfig:
    """Configurações para Fase 4 - Exploração"""
    timeout: int = 45
    max_exploits_per_vuln: int = 3
    safe_mode: bool = True
    proof_of_concept_only: bool = True
    
    # Limites de segurança
    max_payload_size: int = 1024
    allowed_methods: List[str] = field(default_factory=lambda: ["GET", "POST"])
    forbidden_payloads: List[str] = field(default_factory=lambda: [
        "rm -rf", "format c:", "DROP TABLE", "DELETE FROM"
    ])

@dataclass
class PosExploracaoConfig:
    """Configurações para Fase 5 - Pós-exploração"""
    analise_profunda: bool = True
    incluir_cenarios_hipoteticos: bool = True
    avaliar_movimento_lateral: bool = True
    
    # Análise de risco
    risk_matrix: Dict = field(default_factory=lambda: {
        "critical": {"score": 10, "priority": 1},
        "high": {"score": 7, "priority": 2},
        "medium": {"score": 4, "priority": 3},
        "low": {"score": 1, "priority": 4}
    })

@dataclass
class RelatorioConfig:
    """Configurações para Fase 6 - Relatório"""
    incluir_graficos: bool = True
    formato_saida: List[str] = field(default_factory=lambda: ["json", "html"])
    nivel_detalhamento: str = "completo"
    incluir_recomendacoes: bool = True
    
    # Templates
    template_html: str = "default"
    include_executive_summary: bool = True
    include_technical_details: bool = True
    
    # Métricas
    calculate_cvss: bool = True
    include_business_impact: bool = True

# Configuração padrão global
DEFAULT_CONFIG = GlobalConfig()

# Configurações por fase
DEFAULT_RECON_CONFIG = ReconConfig()
DEFAULT_ENUM_CONFIG = EnumConfig()
DEFAULT_VULN_CONFIG = VulnConfig()
DEFAULT_EXPLORACAO_CONFIG = ExploracaoConfig()
DEFAULT_POS_EXPLORACAO_CONFIG = PosExploracaoConfig()
DEFAULT_RELATORIO_CONFIG = RelatorioConfig()

# Wordlists padrão
DEFAULT_WORDLISTS = {
    "directories": [
        "admin", "api", "app", "assets", "backup", "blog", "cdn", "config",
        "dashboard", "dev", "docs", "ftp", "git", "help", "images", "login",
        "mail", "mobile", "news", "old", "panel", "portal", "secure", "shop",
        "staging", "static", "support", "test", "upload", "user", "www"
    ],
    "files": [
        "index", "admin", "login", "config", "database", "backup", "test",
        "api", "robots", "sitemap", "phpinfo", "info", "readme", "changelog"
    ],
    "parameters": [
        "id", "user", "username", "email", "password", "token", "key", "api_key",
        "search", "q", "query", "page", "limit", "offset", "sort", "order",
        "filter", "category", "type", "status", "action", "cmd", "command"
    ]
}

# User agents para rotação
USER_AGENTS = [
    "Guardian-IA/1.0 (Security Scanner)",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
]

# Status codes considerados interessantes
INTERESTING_STATUS_CODES = [200, 201, 204, 301, 302, 307, 308, 401, 403, 405, 500, 502, 503]

# Extensões de arquivo para busca
FILE_EXTENSIONS = [
    "php", "asp", "aspx", "jsp", "html", "htm", "js", "css", "json", "xml",
    "txt", "log", "bak", "old", "tmp", "zip", "tar", "gz", "sql", "db"
]

# Portas comuns para scan
COMMON_PORTS = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080, 8443, 8888]

def get_config_for_target(target: str) -> Dict:
    """Retorna configuração específica para um alvo"""
    return {
        "target": target,
        "output_dir": DEFAULT_CONFIG.results_dir / target.replace(".", "_"),
        "global": DEFAULT_CONFIG,
        "recon": DEFAULT_RECON_CONFIG,
        "enum": DEFAULT_ENUM_CONFIG,
        "vuln": DEFAULT_VULN_CONFIG,
        "exploracao": DEFAULT_EXPLORACAO_CONFIG,
        "pos_exploracao": DEFAULT_POS_EXPLORACAO_CONFIG,
        "relatorio": DEFAULT_RELATORIO_CONFIG
    }
