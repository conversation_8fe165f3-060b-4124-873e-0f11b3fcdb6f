# utils.py (Versão com Caminhos Absolutos)

import subprocess
import os
import shutil

class Cores:
    VERDE = '\033[92m'; VERMELHO = '\033[91m'; AZUL = '\033[94m'
    AMARELO = '\033[93m'; FIM = '\033[0m'

def print_info(mensagem): print(f"{Cores.AZUL}[*] {mensagem}{Cores.FIM}")
def print_sucesso(mensagem): print(f"{Cores.VERDE}[+] {mensagem}{Cores.FIM}")
def print_erro(mensagem): print(f"{Cores.VERMELHO}[!] {mensagem}{Cores.FIM}")
def print_aviso(mensagem): print(f"{Cores.AMARELO}[?] {mensagem}{Cores.FIM}")

def ferramenta_existe(nome_ferramenta):
    return shutil.which(nome_ferramenta) is not None

# **** MUDANÇA AQUI ****
# A função agora recebe um caminho base para os resultados.
def criar_diretorio_alvo(alvo, base_path):
    caminho_completo = os.path.join(base_path, alvo)
    print_info(f"Verificando/criando diretório de resultados em: {caminho_completo}")
    os.makedirs(caminho_completo, exist_ok=True)
    return caminho_completo # Retorna o caminho absoluto

def executar_comando(comando: list, arquivo_saida: str, timeout=600):
    ferramenta = comando[0]
    if not ferramenta_existe(ferramenta) and not os.path.exists(ferramenta):
        print_erro(f"A ferramenta ou script '{ferramenta}' não foi encontrada.")
        return False
    
    print(f"    -> Executando: {' '.join(comando)}")
    try:
        with open(arquivo_saida, "w") as f_saida:
            processo = subprocess.run(
                comando, stdout=f_saida, stderr=subprocess.PIPE, text=True, check=True, timeout=timeout
            )
        if os.path.getsize(arquivo_saida) == 0:
            print_aviso(f"Comando executado, mas o arquivo de saída '{os.path.basename(arquivo_saida)}' está vazio.")
        else:
            print_sucesso(f"Resultados salvos em: {arquivo_saida}")
        return True
    except subprocess.TimeoutExpired:
        print_erro(f"O comando excedeu o tempo limite.")
        return False
    except subprocess.CalledProcessError as e:
        if e.stderr: print_erro(f"Erro: {e.stderr.strip()}")
        else: print_erro("Comando falhou com um erro silencioso.")
        return False
