#!/usr/bin/env python3
# examples/basic_usage.py - Exemplos de uso do Guardian IA

"""
Exemplos de uso da nova estrutura do Guardian IA
"""

import sys
from pathlib import Path

# Adiciona src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.guardian import GuardianIA
from src.core.config import (
    DEFAULT_RECON_CONFIG, DEFAULT_VULN_CONFIG, 
    get_config_for_target
)

def exemplo_execucao_completa():
    """Exemplo de execução completa de todas as fases"""
    print("🔥 EXEMPLO 1: Execução Completa")
    print("-" * 50)
    
    # Cria instância do Guardian IA
    guardian = GuardianIA("exemplo.com")
    
    # Executa todas as fases
    resultado = guardian.execute_all_phases()
    
    if resultado["sucesso"]:
        print("✅ Execução concluída com sucesso!")
        print(f"📊 Fases concluídas: {resultado['completed_phases']}")
        print(f"⏱️  Tempo total: {resultado['execution_time']}")
    else:
        print(f"❌ Execução falhou: {resultado['erro']}")

def exemplo_fases_especificas():
    """Exemplo de execução de fases específicas"""
    print("\n🎯 EXEMPLO 2: Fases Específicas")
    print("-" * 50)
    
    guardian = GuardianIA("exemplo.com")
    
    # Executa apenas reconhecimento
    print("🔍 Executando Fase 1 - Reconhecimento...")
    resultado_fase1 = guardian.execute_phase_1()
    
    if resultado_fase1["sucesso"]:
        print("✅ Fase 1 concluída!")
        
        # Executa enumeração
        print("🎯 Executando Fase 2 - Enumeração...")
        resultado_fase2 = guardian.execute_phase_2()
        
        if resultado_fase2["sucesso"]:
            print("✅ Fase 2 concluída!")
        else:
            print(f"❌ Fase 2 falhou: {resultado_fase2['erro']}")
    else:
        print(f"❌ Fase 1 falhou: {resultado_fase1['erro']}")

def exemplo_configuracao_personalizada():
    """Exemplo com configuração personalizada"""
    print("\n⚙️  EXEMPLO 3: Configuração Personalizada")
    print("-" * 50)
    
    # Configuração personalizada
    config_personalizada = {
        "global": {
            "safe_mode": True,
            "max_threads": 20,
            "log_level": "DEBUG"
        },
        "recon": {
            "timeout": 30,
            "max_subdominios": 500,
            "threads": 20
        },
        "vuln": {
            "sqlmap_risk": 1,
            "safe_mode": True,
            "incluir_nuclei": True
        }
    }
    
    # Cria Guardian IA com configuração personalizada
    guardian = GuardianIA("exemplo.com", custom_config=config_personalizada)
    
    print("✅ Guardian IA configurado com parâmetros personalizados")
    print(f"🔧 Threads: {guardian.config['recon']['threads']}")
    print(f"🛡️  Modo seguro: {guardian.config['global']['safe_mode']}")
    print(f"📝 Log level: {guardian.config['global']['log_level']}")

def exemplo_monitoramento_status():
    """Exemplo de monitoramento de status"""
    print("\n📊 EXEMPLO 4: Monitoramento de Status")
    print("-" * 50)
    
    guardian = GuardianIA("exemplo.com")
    
    # Obtém status inicial
    status = guardian.get_status()
    print(f"🎯 Alvo: {status['target']}")
    print(f"🔄 Fase atual: {status['current_phase']}")
    print(f"✅ Fases concluídas: {len(status['completed_phases'])}")
    print(f"❌ Fases falhadas: {len(status['failed_phases'])}")
    print(f"🏃 Em execução: {status['is_running']}")

def exemplo_configuracao_de_arquivo():
    """Exemplo carregando configuração de arquivo"""
    print("\n📄 EXEMPLO 5: Configuração de Arquivo")
    print("-" * 50)
    
    # Simula carregamento de configuração
    config_file = Path("config/example_config.json")
    
    if config_file.exists():
        import json
        with open(config_file, 'r') as f:
            config_from_file = json.load(f)
        
        guardian = GuardianIA("exemplo.com", custom_config=config_from_file)
        print("✅ Configuração carregada do arquivo")
        print(f"📁 Arquivo: {config_file}")
    else:
        print(f"⚠️  Arquivo de configuração não encontrado: {config_file}")

def exemplo_uso_individual_fases():
    """Exemplo de uso individual das classes de fase"""
    print("\n🔧 EXEMPLO 6: Uso Individual das Fases")
    print("-" * 50)
    
    try:
        # Import das fases individuais
        from src.phases.fase1_recon import ReconhecimentoEstrategico
        from src.phases.fase3_vulnerabilidades import AnalisadorVulnerabilidades
        
        # Uso direto da Fase 1
        print("🔍 Usando Fase 1 diretamente...")
        recon = ReconhecimentoEstrategico(
            alvo="exemplo.com",
            diretorio_saida="./results/exemplo_com",
            config=DEFAULT_RECON_CONFIG
        )
        
        print("✅ Fase 1 inicializada")
        
        # Uso direto da Fase 3
        print("🔬 Usando Fase 3 diretamente...")
        vuln_analyzer = AnalisadorVulnerabilidades(
            alvo="exemplo.com",
            diretorio_saida="./results/exemplo_com",
            config=DEFAULT_VULN_CONFIG
        )
        
        print("✅ Fase 3 inicializada")
        
    except ImportError as e:
        print(f"❌ Erro no import: {e}")
        print("ℹ️  Certifique-se de que os arquivos das fases existem em src/phases/")

def exemplo_configuracao_avancada():
    """Exemplo de configuração avançada"""
    print("\n🚀 EXEMPLO 7: Configuração Avançada")
    print("-" * 50)
    
    # Configuração para ambiente de produção
    config_producao = {
        "global": {
            "safe_mode": True,
            "max_threads": 100,
            "requests_per_second": 20,
            "log_level": "INFO"
        },
        "recon": {
            "timeout": 120,
            "max_subdominios": 5000,
            "threads": 80,
            "use_passive_only": False
        },
        "vuln": {
            "timeout": 180,
            "sqlmap_risk": 2,
            "safe_mode": True,
            "max_urls_per_tool": 500,
            "nuclei_config": {
                "severity": ["critical", "high"],
                "timeout": 60,
                "rate_limit": 300
            }
        },
        "relatorio": {
            "incluir_graficos": True,
            "formato_saida": ["json", "html", "pdf"],
            "include_executive_summary": True,
            "calculate_cvss": True
        }
    }
    
    guardian = GuardianIA("exemplo.com", custom_config=config_producao)
    print("✅ Configuração avançada aplicada")
    print(f"🧵 Max threads: {guardian.config['global']['max_threads']}")
    print(f"🎯 Max subdomínios: {guardian.config['recon']['max_subdominios']}")
    print(f"📊 Formatos de saída: {guardian.config['relatorio']['formato_saida']}")

def main():
    """Executa todos os exemplos"""
    print("🛡️  GUARDIAN IA - EXEMPLOS DE USO")
    print("=" * 80)
    print("Demonstrações da nova estrutura organizada")
    print("=" * 80)
    
    # Lista de exemplos
    exemplos = [
        exemplo_configuracao_personalizada,
        exemplo_monitoramento_status,
        exemplo_configuracao_de_arquivo,
        exemplo_uso_individual_fases,
        exemplo_configuracao_avancada
    ]
    
    # Executa exemplos que não requerem execução real
    for exemplo in exemplos:
        try:
            exemplo()
        except Exception as e:
            print(f"❌ Erro no exemplo: {e}")
    
    print("\n" + "=" * 80)
    print("📚 EXEMPLOS CONCLUÍDOS")
    print("=" * 80)
    print("Para executar análises reais:")
    print("python guardian_main.py --target exemplo.com")
    print("\nPara mais opções:")
    print("python guardian_main.py --help")
    print("=" * 80)

if __name__ == "__main__":
    main()
