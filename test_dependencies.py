#!/usr/bin/env python3
# test_dependencies.py - Teste rápido de dependências

def test_imports():
    """Testa se todas as dependências essenciais estão instaladas"""
    print("🧪 Testando dependências do Guardian IA...")
    print("-" * 50)
    
    dependencies = [
        ("requests", "Requisições HTTP"),
        ("json", "Processamento JSON"),
        ("pathlib", "Manipulação de caminhos"),
        ("datetime", "Data e hora"),
        ("subprocess", "Execução de comandos"),
        ("logging", "Sistema de logs"),
        ("os", "Sistema operacional"),
        ("dataclasses", "Classes de dados"),
        ("typing", "Type hints")
    ]
    
    failed = []
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module:<15} - {description}")
        except ImportError as e:
            print(f"❌ {module:<15} - ERRO: {e}")
            failed.append(module)
    
    print("-" * 50)
    
    if failed:
        print(f"❌ {len(failed)} dependência(s) em falta:")
        for module in failed:
            print(f"   - {module}")
        print("\nPara instalar:")
        print("pip install " + " ".join(failed))
        return False
    else:
        print("✅ Todas as dependências essenciais estão instaladas!")
        return True

def test_guardian_files():
    """Testa se os arquivos do Guardian IA existem"""
    print("\n📁 Verificando arquivos do Guardian IA...")
    print("-" * 50)
    
    from pathlib import Path
    
    files = [
        ("guardian_main.py", "Arquivo principal"),
        ("src/__init__.py", "Pacote principal"),
        ("src/core/guardian.py", "Core - Guardian IA"),
        ("src/core/config.py", "Core - Configurações"),
        ("src/core/logger.py", "Core - Sistema de logs"),
        ("src/utils/file_utils.py", "Utils - Arquivos"),
        ("src/utils/command_utils.py", "Utils - Comandos")
    ]
    
    missing = []
    
    for filename, description in files:
        if Path(filename).exists():
            print(f"✅ {filename:<30} - {description}")
        else:
            print(f"❌ {filename:<30} - ARQUIVO NÃO ENCONTRADO")
            missing.append(filename)
    
    print("-" * 50)
    
    if missing:
        print(f"❌ {len(missing)} arquivo(s) em falta:")
        for filename in missing:
            print(f"   - {filename}")
        return False
    else:
        print("✅ Todos os arquivos do Guardian IA estão presentes!")
        return True

def test_basic_functionality():
    """Testa funcionalidade básica"""
    print("\n⚙️  Testando funcionalidade básica...")
    print("-" * 50)
    
    try:
        # Testa import do módulo principal
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent / "src"))

        from src.core.guardian import GuardianIA
        print("✅ Import Guardian IA - OK")

        # Testa criação de diretório
        test_dir = Path("test_output")
        test_dir.mkdir(exist_ok=True)
        print("✅ Criação de diretórios - OK")

        # Remove diretório de teste
        test_dir.rmdir()
        print("✅ Limpeza de teste - OK")

        print("-" * 50)
        print("✅ Funcionalidade básica testada com sucesso!")
        return True

    except Exception as e:
        print(f"❌ Erro no teste de funcionalidade: {e}")
        return False

def main():
    """Função principal de teste"""
    print("🛡️  GUARDIAN IA - TESTE DE DEPENDÊNCIAS")
    print("=" * 60)
    
    # Testa imports
    imports_ok = test_imports()
    
    # Testa arquivos
    files_ok = test_guardian_files()
    
    # Testa funcionalidade
    functionality_ok = test_basic_functionality()
    
    print("\n" + "=" * 60)
    
    if imports_ok and files_ok and functionality_ok:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ O Guardian IA está pronto para uso!")
        print("\nPara executar:")
        print("python guardian_main.py --target exemplo.com")
    else:
        print("⚠️  ALGUNS TESTES FALHARAM!")
        print("Execute o setup para corrigir problemas:")
        print("python setup_guardian.py")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
