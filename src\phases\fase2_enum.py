# src/phases/fase2_enum.py - Fase 2: Enumeração Ativa e Mapeamento da Superfície de Ataque

import os
import json
import subprocess
import re
import time
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse

# Imports da nova estrutura
from ..core.logger import get_logger
from ..core.config import DEFAULT_ENUM_CONFIG
from ..utils.file_utils import save_json, load_json, save_csv
from ..utils.command_utils import CommandExecutor, ToolRunner

@dataclass
class EnumConfig:
    # ... (dataclass EnumConfig exatamente como você escreveu)
    timeout: int = 30; threads: int = 50; max_urls_per_domain: int = 100
    dirsearch_threads: int = 50; max_depth: int = 3
    wordlists: Optional[List[str]] = None; extensions: Optional[List[str]] = None
    def __post_init__(self):
        if self.wordlists is None: self.wordlists = ["/usr/share/wordlists/dirb/common.txt"]
        if self.extensions is None: self.extensions = ["php", "html", "js", "txt", "json", "xml"]

@dataclass
class DirectoryInfo:
    # ... (dataclass DirectoryInfo exatamente como você escreveu)
    url: str; status_code: int; size: Optional[int] = None
    discovered_by: str = "unknown"

@dataclass
class ParameterInfo:
    # ... (dataclass ParameterInfo exatamente como você escreveu)
    url: str; parameter: str; method: str = "GET"
    example_value: Optional[str] = None; discovered_by: str = "unknown"

@dataclass
class VulnerabilityPattern:
    # ... (dataclass VulnerabilityPattern exatamente como você escreveu)
    url: str; pattern_type: str; confidence: str = "low"
    description: Optional[str] = None

class EnumeracaoAtiva:
    """Classe principal para enumeração ativa e mapeamento"""
    def __init__(self, alvo: str, diretorio_saida: str, config: EnumConfig = None):
        self._criar_estrutura_diretorios()
        self.alvo = alvo
        self.diretorio_saida = Path(diretorio_saida)
        self.config = config or EnumConfig()
        self.logger = self._configurar_logging()
        self.urls_ativas = self._carregar_urls_ativas()
    
    def _configurar_logging(self):
        """Configura logging detalhado"""
        self.logger = get_logger(f"enum_{self.alvo}")
        return self.logger

    def _criar_estrutura_diretorios(self):
        #...(código de criação de diretórios igual ao que você forneceu)...
        for subdir in ["diretorios", "parametros", "crawling", "vulnerabilidades", "relatorios_fase2"]:
            (self.diretorio_saida / subdir).mkdir(exist_ok=True, parents=True)

    def _carregar_urls_ativas(self):
        """Carrega URLs ativas do relatório JSON da Fase 1"""
        # AJUSTE DE COMPATIBILIDADE: Caminho corrigido para o output da nossa Fase 1
        arquivo_httpx = self.diretorio_saida / "subdominios" / "ativos_detalhados.json"
        if not arquivo_httpx.exists():
            self.logger.warning("Arquivo de URLs ativas da Fase 1 não encontrado, a enumeração será limitada.")
            return []
        
        urls_carregadas = []
        try:
            with open(arquivo_httpx, 'r') as f:
                for linha in f:
                    if linha.strip():
                        data = json.loads(linha)
                        if data.get('status_code') and 200 <= data.get('status_code') < 400:
                            urls_carregadas.append(data.get('url', ''))
            self.logger.info(f"Carregadas {len(urls_carregadas)} URLs ativas da Fase 1")
            return urls_carregadas
        except Exception as e:
            self.logger.error(f"Erro ao carregar URLs ativas: {str(e)}"); return []

    # O _executar_ferramenta_segura e todas as outras funções de lógica que você escreveu
    # podem ser coladas aqui diretamente. Elas estão muito bem escritas.
    # Por exemplo:
    def _executar_ferramenta_segura(self, comando: List[str], arquivo_saida: str) -> bool:
        try:
            self.logger.info(f"Executando: {' '.join(comando)}")
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                processo = subprocess.run(
                    comando, stdout=f, stderr=subprocess.PIPE,
                    timeout=self.config.timeout * 2, text=True, encoding='utf-8'
                )
            return processo.returncode == 0
        except Exception as e:
            self.logger.error(f"Erro em '{comando[0]}': {e}"); return False

    def descoberta_diretorios(self) -> List[DirectoryInfo]:
        self.logger.info("Iniciando descoberta de diretórios com Feroxbuster...")
        # Lógica completa do seu feroxbuster/gobuster aqui
        return []

    def crawling_inteligente(self) -> List[str]:
        self.logger.info("Iniciando crawling inteligente...")
        # Lógica completa do seu katana/gospider/hakrawler aqui
        return []
        
    def descoberta_parametros(self, urls_crawling: List[str]) -> List[ParameterInfo]:
        self.logger.info("Iniciando descoberta de parâmetros...")
        # Lógica completa do seu ParamSpider/Arjun aqui
        return []

    def analise_vulnerabilidades_gf(self, urls: List[str]) -> List[VulnerabilityPattern]:
        self.logger.info("Analisando padrões de vulnerabilidades com GF...")
        # Lógica completa do seu GF aqui
        return []

    def gerar_relatorio_completo(self, **kwargs):
        self.logger.info("Gerando relatório da Fase 2...")
        # Lógica completa de geração de relatório aqui

    def executar_fase_completa(self):
        """Executa o fluxo de trabalho completo da Fase 2."""
        self.logger.info(f"🚀 INICIANDO FASE 2 PARA O ALVO: {self.alvo}")
        if not self.urls_ativas:
            self.logger.warning("Nenhuma URL ativa encontrada da Fase 1 para processar.")
            # Ainda podemos tentar o crawling no domínio principal
            urls_crawling = self.crawling_inteligente()
        else:
            urls_crawling = self.crawling_inteligente()

        diretorios = self.descoberta_diretorios()
        parametros = self.descoberta_parametros(urls_crawling)
        vulnerabilidades = self.analise_vulnerabilidades_gf(urls_crawling)
        
        self.gerar_relatorio_completo(
            diretorios=diretorios, parametros=parametros, 
            vulnerabilidades=vulnerabilidades, urls_crawling=urls_crawling
        )
        self.logger.info("✅ FASE 2 CONCLUÍDA")
        return {"sucesso": True}

def run(alvo: str, diretorio_saida: str, config_dict: Dict = None):
    """Interface principal para executar a Fase 2"""
    config = EnumConfig(**config_dict) if config_dict else EnumConfig()
    enumeracao = EnumeracaoAtiva(alvo, diretorio_saida, config)
    return enumeracao.executar_fase_completa()
