# At least one of these options has to be specified to set the source to
# get target URLs from.
[Target]

# Target URL.
# Example: http://*************/sqlmap/mysql/get_int.php?id=1&cat=2
url =

# Direct connection to the database.
# Examples:
#   mysql://USER:PASSWORD@DBMS_IP:DBMS_PORT/DATABASE_NAME
#   oracle://USER:PASSWORD@DBMS_IP:DBMS_PORT/DATABASE_SID
direct = 

# Parse targets from Burp or WebScarab logs
# Valid: Burp proxy (http://portswigger.net/suite/) requests log file path
# or WebScarab proxy (http://www.owasp.org/index.php/Category:OWASP_WebScarab_Project)
# 'conversations/' folder path
logFile = 

# Scan multiple targets enlisted in a given textual file
bulkFile =

# Load HTTP request from a file
# Example (file content): POST /login.jsp HTTP/1.1\nHost: example.com\nUser-Agent: Mozilla/4.0\n\nuserid=joe&password=guessme
requestFile = 

# Rather than providing a target URL, let Google return target
# hosts as result of your Google dork expression. For a list of Google
# dorks see Google Hacking Database at
# https://www.exploit-db.com/google-hacking-database
# Example: +ext:php +inurl:"&id=" +intext:"powered by "
googleDork = 


# These options can be used to specify how to connect to the target URL.
[Request]

# Force usage of given HTTP method (e.g. PUT).
method = 

# Data string to be sent through POST (e.g. "id=1").
data = 

# Character used for splitting parameter values (e.g. &).
paramDel = 

# HTTP Cookie header value (e.g. "PHPSESSID=a8d127e..").
cookie = 

# Character used for splitting cookie values (e.g. ;).
cookieDel = 

# Live cookies file used for loading up-to-date values.
liveCookies = 

# File containing cookies in Netscape/wget format.
loadCookies = 

# Ignore Set-Cookie header from response.
# Valid: True or False
dropSetCookie = False

# Use HTTP version 2 (experimental).
# Valid: True or False
http2 = False

# HTTP User-Agent header value. Useful to fake the HTTP User-Agent header value
# at each HTTP request.
# sqlmap will also test for SQL injection on the HTTP User-Agent value.
agent =

# Imitate smartphone through HTTP User-Agent header.
# Valid: True or False
mobile = False

# Use randomly selected HTTP User-Agent header value.
# Valid: True or False
randomAgent = False

# HTTP Host header value.
host = 

# HTTP Referer header. Useful to fake the HTTP Referer header value at
# each HTTP request.
referer = 

# Extra HTTP headers
headers = Accept: text/xml,application/xml,application/xhtml+xml,text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5
 Accept-Language: en-us,en;q=0.5
 Accept-Charset: ISO-8859-15,utf-8;q=0.7,*;q=0.7

# HTTP Authentication type. Useful only if the target URL requires
# HTTP Basic, Digest, Bearer or NTLM authentication and you have such data.
# Valid: Basic, Digest, Bearer, NTLM or PKI
authType = 

# HTTP authentication credentials. Useful only if the target URL requires
# HTTP Basic, Digest, Token or NTLM authentication and you have such data.
# Syntax: username:password
authCred = 

# HTTP Authentication PEM private/cert key file. Useful only if the target URL requires
# PKI authentication and you have such data.
# Syntax: key_file
authFile = 

# Abort on (problematic) HTTP error code (e.g. 401).
# Valid: string
abortCode =

# Ignore (problematic) HTTP error code (e.g. 401).
# Valid: string
ignoreCode =

# Ignore system default proxy settings.
# Valid: True or False
ignoreProxy = False

# Ignore redirection attempts.
# Valid: True or False
ignoreRedirects = False

# Ignore connection timeouts.
# Valid: True or False
ignoreTimeouts = False

# Use a proxy to connect to the target URL.
# Syntax: (http|https|socks4|socks5)://address:port
proxy = 

# Proxy authentication credentials. Useful only if the proxy requires
# Basic or Digest authentication and you have such data.
# Syntax: username:password
proxyCred =

# Load proxy list from a file
proxyFile =

# Use Tor anonymity network.
# Valid: True or False
tor = False

# Set Tor proxy port other than default.
# Valid: integer
# torPort =

# Set Tor proxy type.
# Valid: HTTP, SOCKS4, SOCKS5
torType = SOCKS5

# Check to see if Tor is used properly.
# Valid: True or False
checkTor = False

# Delay in seconds between each HTTP request.
# Valid: float
# Default: 0
delay = 0

# Seconds to wait before timeout connection.
# Valid: float
# Default: 30
timeout = 30

# Maximum number of retries when the HTTP connection timeouts.
# Valid: integer
# Default: 3
retries = 3

# Retry request on regexp matching content.
retryOn =

# Randomly change value for the given parameter.
rParam = 

# URL address to visit frequently during testing.
# Example: http://*************/index.html
safeUrl = 

# POST data to send to a safe URL.
# Example: username=admin&password=passw0rd!
safePost = 

# Load safe HTTP request from a file.
safeReqFile = 

# Regular requests between visits to a safe URL (default 0).
# Valid: integer
# Default: 0
safeFreq = 0

# Skip URL encoding of payload data.
# Valid: True or False
skipUrlEncode = False

# Parameter used to hold anti-CSRF token.
csrfToken = 

# URL address to visit to extract anti-CSRF token
csrfUrl = 

# HTTP method to use during anti-CSRF token page visit.
csrfMethod =

# POST data to send during anti-CSRF token page visit.
csrfData =

# Retries for anti-CSRF token retrieval.
csrfRetries =

# Force usage of SSL/HTTPS
# Valid: True or False
forceSSL = False

# Use HTTP chunked transfer encoded requests.
# Valid: True or False
chunked = False

# Use HTTP parameter pollution.
# Valid: True or False
hpp = False

# Evaluate provided Python code before the request.
# Example: import hashlib;id2=hashlib.md5(id).hexdigest()
evalCode = 

# These options can be used to optimize the performance of sqlmap.
[Optimization]

# Use all optimization options.
# Valid: True or False
optimize = False

# Predict common queries output.
# Valid: True or False
predictOutput = False

# Use persistent HTTP(s) connections.
keepAlive = False

# Retrieve page length without actual HTTP response body.
# Valid: True or False
nullConnection = False

# Maximum number of concurrent HTTP(s) requests (handled with Python threads)
# to be used in the inference SQL injection attack.
# Valid: integer
# Default: 1
threads = 1


# These options can be used to specify which parameters to test for,
# provide custom injection payloads and optional tampering scripts.
[Injection]

# Testable parameter(s) comma separated. By default all GET/POST/Cookie
# parameters and HTTP User-Agent are tested by sqlmap.
testParameter = 

# Skip testing for given parameter(s).
skip =

# Skip testing parameters that not appear to be dynamic.
# Valid: True or False
skipStatic = False

# Regexp to exclude parameters from testing (e.g. "ses").
paramExclude =

# Select testable parameter(s) by place (e.g. "POST").
paramFilter =

# Force back-end DBMS to provided value. If this option is set, the back-end
# DBMS identification process will be minimized as needed.
# If not set, sqlmap will detect back-end DBMS automatically by default.
# Valid: mssql, mysql, mysql 4, mysql 5, oracle, pgsql, sqlite, sqlite3,
# access, firebird, maxdb, sybase
dbms = 

# DBMS authentication credentials (user:password). Useful if you want to
# run SQL statements as another user, the back-end database management
# system is PostgreSQL or Microsoft SQL Server and the parameter is
# vulnerable by stacked queries SQL injection or you are connecting directly
# to the DBMS (-d switch).
# Syntax: username:password
dbmsCred = 

# Force back-end DBMS operating system to provided value. If this option is
# set, the back-end DBMS identification process will be minimized as
# needed.
# If not set, sqlmap will detect back-end DBMS operating system
# automatically by default.
# Valid: linux, windows
os = 

# Use big numbers for invalidating values.
# Valid: True or False
invalidBignum = False

# Use logical operations for invalidating values.
# Valid: True or False
invalidLogical = False

# Use random strings for invalidating values.
# Valid: True or False
invalidString = False

# Turn off payload casting mechanism
# Valid: True or False
noCast = False

# Turn off string escaping mechanism
# Valid: True or False
noEscape = False

# Injection payload prefix string.
prefix = 

# Injection payload suffix string.
suffix = 

# Use given script(s) for tampering injection data.
tamper = 


# These options can be used to specify how to parse and compare page
# content from HTTP responses when using blind SQL injection technique.
[Detection]

# Level of tests to perform.
# The higher the value is, the higher the number of HTTP(s) requests are
# as well as the better chances to detect a tricky SQL injection.
# Valid: Integer between 1 and 5
# Default: 1
level = 1

# Risk of tests to perform.
# Note: boolean-based blind SQL injection tests with AND are considered
# risk 1, with OR are considered risk 3.
# Valid: Integer between 1 and 3
# Default: 1
risk = 1

# String to match within the raw response when the query is evaluated to 
# True, only needed if the page content dynamically changes at each refresh.
# Refer to the user's manual for further details.
string = 

# String to match within the raw response when the query is evaluated to 
# False, only needed if the page content dynamically changes at each refresh.
# Refer to the user's manual for further details.
notString = 

# Regular expression to match within the raw response when the query is
# evaluated to True, only needed if the needed if the page content
# dynamically changes at each refresh.
# Refer to the user's manual for further details.
# Valid: regular expression with Python syntax
# (http://www.python.org/doc/2.5.2/lib/re-syntax.html)
regexp = 

# HTTP response code to match when the query is True.
# Valid: Integer
# Example: 200 (assuming any False statement returns a different response
# code)
# code = 

# Conduct thorough tests only if positive heuristic(s).
# Valid: True or False
smart = False

# Compare pages based only on the textual content.
# Valid: True or False
textOnly = False

# Compare pages based only on their titles.
# Valid: True or False
titles = False


# These options can be used to tweak testing of specific SQL injection
# techniques.
[Techniques]

# SQL injection techniques to use.
# Valid: a string composed by B, E, U, S, T and Q where:
# B: Boolean-based blind SQL injection
# E: Error-based SQL injection
# U: UNION query SQL injection
# S: Stacked queries SQL injection
# T: Time-based blind SQL injection
# Q: Inline SQL injection
# Example: ES (means test for error-based and stacked queries SQL
# injection types only)
# Default: BEUSTQ (means test for all SQL injection types - recommended)
technique = BEUSTQ

# Seconds to delay the response from the DBMS.
# Valid: integer
# Default: 5
timeSec = 5

# Range of columns to test for.
# Valid: range of integers
# Example: 1-10
uCols = 

# Character to use for bruteforcing number of columns.
# Valid: string
# Example: NULL
uChar = 

# Table to use in FROM part of UNION query SQL injection.
# Valid: string
# Example: INFORMATION_SCHEMA.COLLATIONS
uFrom = 

# Column values to use for UNION query SQL injection.
# Valid: string
# Example: NULL,1,*,NULL
uValues =

# Domain name used for DNS exfiltration attack.
# Valid: string
dnsDomain =

# Resulting page URL searched for second-order response.
# Valid: string
secondUrl =

# Load second-order HTTP request from file.
# Valid: string
secondReq =


[Fingerprint]

# Perform an extensive back-end database management system fingerprint
# based on various techniques.
# Valid: True or False
extensiveFp = False


# These options can be used to enumerate the back-end database
# management system information, structure and data contained in the
# tables. Moreover you can run your own SQL statements.
[Enumeration]

# Retrieve everything
# Valid: True or False
getAll = False

# Retrieve back-end database management system banner.
# Valid: True or False
getBanner = False

# Retrieve back-end database management system current user.
# Valid: True or False
getCurrentUser = False

# Retrieve back-end database management system current database.
# Valid: True or False
getCurrentDb = False

# Retrieve back-end database management system server hostname.
# Valid: True or False
getHostname = False

# Detect if the DBMS current user is DBA.
# Valid: True or False
isDba = False

# Enumerate back-end database management system users.
# Valid: True or False
getUsers = False

# Enumerate back-end database management system users password hashes.
# Valid: True or False
getPasswordHashes = False

# Enumerate back-end database management system users privileges.
# Valid: True or False
getPrivileges = False

# Enumerate back-end database management system users roles.
# Valid: True or False
getRoles = False

# Enumerate back-end database management system databases.
# Valid: True or False
getDbs = False

# Enumerate back-end database management system database tables.
# Optional: db
# Valid: True or False
getTables = False

# Enumerate back-end database management system database table columns.
# Optional: db, tbl, col
# Valid: True or False
getColumns = False

# Enumerate back-end database management system schema.
# Valid: True or False
getSchema = False

# Retrieve number of entries for table(s).
# Valid: True or False
getCount = False

# Dump back-end database management system database table entries.
# Requires: tbl and/or col
# Optional: db
# Valid: True or False
dumpTable = False

# Dump all back-end database management system databases tables entries.
# Valid: True or False
dumpAll = False

# Search column(s), table(s) and/or database name(s).
# Requires: db, tbl or col
# Valid: True or False
search = False

# Check for database management system database comments during enumeration.
# Valid: True or False
getComments = False

# Retrieve SQL statements being run on database management system.
# Valid: True or False
getStatements = False

# Back-end database management system database to enumerate.
db = 

# Back-end database management system database table(s) to enumerate.
tbl = 

# Back-end database management system database table column(s) to enumerate.
col = 

# Back-end database management system identifiers (database(s), table(s) and column(s)) to not enumerate.
exclude = 

# Pivot column name.
pivotColumn =

# Use WHERE condition while table dumping (e.g. "id=1").
dumpWhere = 

# Back-end database management system database user to enumerate.
user = 

# Exclude DBMS system databases when enumerating tables.
# Valid: True or False
excludeSysDbs = False

# First query output entry to retrieve
# Valid: integer
# Default: 0 (sqlmap will start to retrieve the table dump entries from
# first one)
limitStart = 0

# Last query output entry to retrieve
# Valid: integer
# Default: 0 (sqlmap will detect the number of table dump entries and
# retrieve them until the last)
limitStop = 0

# First query output word character to retrieve
# Valid: integer
# Default: 0 (sqlmap will enumerate the query output from the first
# character)
firstChar = 0

# Last query output word character to retrieve
# Valid: integer
# Default: 0 (sqlmap will enumerate the query output until the last
# character)
lastChar = 0

# SQL statement to be executed.
# Example: SELECT 'foo', 'bar'
sqlQuery = 

# Prompt for an interactive SQL shell.
# Valid: True or False
sqlShell = False

# Execute SQL statements from given file(s).
sqlFile = 


# These options can be used to run brute force checks.
[Brute force]

# Check existence of common tables.
# Valid: True or False
commonTables = False

# Check existence of common columns.
# Valid: True or False
commonColumns = False

# Check existence of common files.
# Valid: True or False
commonFiles = False


# These options can be used to create custom user-defined functions.
[User-defined function]

# Inject custom user-defined functions
# Valid: True or False
udfInject = False

# Local path of the shared library
shLib = 


# These options can be used to access the back-end database management
# system underlying file system.
[File system]

# Read a specific file from the back-end DBMS underlying file system.
# Examples: /etc/passwd or C:\boot.ini
fileRead = 

# Write a local file to a specific path on the back-end DBMS underlying
# file system.
# Example: /tmp/sqlmap.txt or C:\WINNT\Temp\sqlmap.txt
fileWrite = 

# Back-end DBMS absolute filepath to write the file to.
fileDest = 


# These options can be used to access the back-end database management
# system underlying operating system.
[Takeover]

# Execute an operating system command.
# Valid: operating system command
osCmd = 

# Prompt for an interactive operating system shell.
# Valid: True or False
osShell = False

# Prompt for an out-of-band shell, Meterpreter or VNC.
# Valid: True or False
osPwn = False

# One click prompt for an out-of-band shell, Meterpreter or VNC.
# Valid: True or False
osSmb = False

# Microsoft SQL Server 2000 and 2005 'sp_replwritetovarbin' stored
# procedure heap-based buffer overflow (MS09-004) exploitation.
# Valid: True or False
osBof = False

# Database process' user privilege escalation.
# Note: Use in conjunction with osPwn, osSmb or osBof. It will force the
# payload to be Meterpreter.
privEsc = False

# Local path where Metasploit Framework is installed.
# Valid: file system path
msfPath = 

# Remote absolute path of temporary files directory.
# Valid: absolute file system path
tmpPath = 


# These options can be used to access the back-end database management
# system Windows registry.
[Windows]

# Read a Windows registry key value.
# Valid: True or False
regRead = False

# Write a Windows registry key value data.
# Valid: True or False
regAdd = False

# Delete a Windows registry key value.
# Valid: True or False
regDel = False

# Windows registry key.
regKey = 

# Windows registry key value.
regVal = 

# Windows registry key value data.
regData = 

# Windows registry key value type.
regType = 


# These options can be used to set some general working parameters.
[General]

# Load session from a stored (.sqlite) file
# Example: output/www.target.com/session.sqlite
sessionFile = 

# Log all HTTP traffic into a textual file.
trafficFile = 

# Abort data retrieval on empty results.
abortOnEmpty = False

# Set predefined answers (e.g. "quit=N,follow=N").
answers =

# Parameter(s) containing Base64 encoded data
base64Parameter =

# Use URL and filename safe Base64 alphabet (Reference: https://en.wikipedia.org/wiki/Base64#URL_applications).
# Valid: True or False
base64Safe = False

# Never ask for user input, use the default behaviour.
# Valid: True or False
batch = False

# Result fields having binary values (e.g. "digest").
binaryFields =

# Check Internet connection before assessing the target.
checkInternet = False

# Clean up the DBMS from sqlmap specific UDF and tables.
# Valid: True or False
cleanup = False

# Crawl the website starting from the target URL.
# Valid: integer
# Default: 0
crawlDepth = 0

# Regexp to exclude pages from crawling (e.g. "logout").
crawlExclude =

# Delimiting character used in CSV output.
# Default: ,
csvDel = ,

# Store dumped data to a custom file.
dumpFile =

# Format of dumped data
# Valid: CSV, HTML or SQLITE
dumpFormat = CSV

# Force character encoding used for data retrieval.
encoding = 

# Retrieve each query output length and calculate the estimated time of
# arrival in real time.
# Valid: True or False
eta = False

# Flush session files for current target.
# Valid: True or False
flushSession = False

# Parse and test forms on target URL.
# Valid: True or False
forms = False

# Ignore query results stored in session file.
# Valid: True or False
freshQueries = False

# Use Google dork results from specified page number.
# Valid: integer
# Default: 1
googlePage = 1

# Use hex conversion during data retrieval.
# Valid: True or False
hexConvert = False

# Custom output directory path.
outputDir =

# Parse and display DBMS error messages from responses.
# Valid: True or False
parseErrors = False

# Use given script(s) for preprocessing of request.
preprocess =

# Use given script(s) for postprocessing of response data.
postprocess =

# Redump entries having unknown character marker (?).
# Valid: True or False
repair = False

# Regular expression for filtering targets from provided Burp.
# or WebScarab proxy log.
# Example: (google|yahoo)
scope = 

# Skip heuristic detection of SQLi/XSS vulnerabilities.
# Valid: True or False
skipHeuristics = False

# Skip heuristic detection of WAF/IPS protection.
# Valid: True or False
skipWaf = False

# Prefix used for temporary tables.
# Default: sqlmap
tablePrefix = sqlmap

# Select tests by payloads and/or titles (e.g. ROW).
testFilter =

# Skip tests by payloads and/or titles (e.g. BENCHMARK).
testSkip =

# Run with a time limit in seconds (e.g. 3600).
timeLimit =

# Disable escaping of DBMS identifiers (e.g. "user").
unsafeNaming = False

# Web server document root directory (e.g. "/var/www").
webRoot =


[Miscellaneous]

# Run host OS command(s) when SQL injection is found.
alert =

# Beep on question and/or when SQL injection is found.
# Valid: True or False
beep = False

# Offline WAF/IPS payload detection testing.
# Valid: True or False
checkPayload = False

# Check for missing (optional) sqlmap dependencies.
# Valid: True or False
dependencies = False

# Disable console output coloring.
# Valid: True or False
disableColoring = False

# Disable hash analysis on table dumps.
# Valid: True or False
disableHashing = False

# Display list of available tamper scripts.
# Valid: True or False
listTampers = False

# Disable logging to a file.
# Valid: True or False
noLogging = False

# Disable console output truncation.
# Valid: True or False
noTruncate = False

# Work in offline mode (only use session data)
# Valid: True or False
offline = False

# Location of CSV results file in multiple targets mode.
resultsFile =

# Local directory for storing temporary files.
tmpDir =

# Adjust options for unstable connections.
# Valid: True or False
unstable = False

# Update sqlmap.
# Valid: True or False
updateAll = False

# Simple wizard interface for beginner users.
# Valid: True or False
wizard = False

# Verbosity level.
# Valid: integer between 0 and 6
# 0: Show only error and critical messages
# 1: Show also warning and info messages
# 2: Show also debug messages
# 3: Show also payloads injected
# 4: Show also HTTP requests
# 5: Show also HTTP responses' headers
# 6: Show also HTTP responses' page content
# Default: 1
verbose = 1
