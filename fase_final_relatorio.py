# fase_final_relatorio.py

import os
import json
from utils import print_info, print_sucesso, print_aviso
from config import LIMPAR_ARQUIVOS_INTERMEDIARIOS

def run(alvo, diretorio_saida):
    """
    Lê os resultados de TODAS as fases, gera um relatório final consolidado
    e limpa os arquivos de trabalho.
    """
    print_info("--- INICIANDO FASE FINAL: GERAÇÃO DE RELATÓRIO E LIMPEZA ---")
    
    # --- Coleta de Dados das Fases 1, 2 e 3 ---
    dados_fase1 = {"subs_encontrados": 0, "subs_ativos": [], "urls_descobertas": 0}
    dados_fase2 = {"gf_patterns": {}}
    dados_fase3 = {"nuclei_findings": []}

    # Coletando dados da Fase 1
    arquivo_httpx_json = f"{diretorio_saida}/subdominios_ativos.json"
    if os.path.exists(arquivo_httpx_json):
        with open(arquivo_httpx_json, "r") as f:
            for line in f:
                try: dados_fase1["subs_ativos"].append(json.loads(line))
                except json.JSONDecodeError: continue
    
    # Coletando dados da Fase 2 (GF)
    padroes_gf = ["xss", "sqli", "lfi", "idor", "redirect", "ssrf"]
    for padrao in padroes_gf:
        arquivo_gf = f"{diretorio_saida}/gf_{padrao}_potencial.txt"
        if os.path.exists(arquivo_gf):
            with open(arquivo_gf, "r") as f:
                linhas = f.readlines()
                if linhas:
                    dados_fase2["gf_patterns"][padrao] = len(linhas)

    # Coletando dados da Fase 3 (Nuclei)
    arquivo_nuclei = f"{diretorio_saida}/nuclei_vulnerabilidades.txt"
    if os.path.exists(arquivo_nuclei):
        with open(arquivo_nuclei, "r") as f:
            dados_fase3["nuclei_findings"] = [line.strip() for line in f]

    # --- Geração do Relatório Markdown Final ---
    path_relatorio_md = f"{diretorio_saida}/RELATORIO_FINAL.md"
    with open(path_relatorio_md, "w") as f:
        f.write(f"# Relatório Final de Análise - {alvo}\n\n")
        f.write("## Sumário da Fase 1: Reconhecimento\n")
        f.write(f"* **Subdomínios Ativos:** {len(dados_fase1['subs_ativos'])}\n")
        
        # Tabela de Hosts Ativos
        f.write("\n### Hosts Ativos e Tecnologias Principais\n")
        f.write("| Host | Status | Título da Página | Tecnologias |\n")
        f.write("| :--- | :--- | :--- | :--- |\n")
        for host in dados_fase1["subs_ativos"][:15]: # Limita a 15 para o relatório não ficar gigante
            f.write(f"| {host.get('url', 'N/A')} | {host.get('status_code', 'N/A')} | {host.get('title', '').replace('|', '')} | {', '.join(host.get('tech', []))} |\n")
        if len(dados_fase1["subs_ativos"]) > 15: f.write("| ... e mais |\n")
        f.write("\n---\n")

        # Sumário da Fase 2
        f.write("## Sumário da Fase 2: Enumeração\n")
        if dados_fase2["gf_patterns"]:
            f.write("URLs com padrões de vulnerabilidade potencial encontradas pelo GF:\n")
            for padrao, contagem in dados_fase2["gf_patterns"].items():
                f.write(f"* **{padrao.upper()}:** {contagem} URLs suspeitas encontradas.\n")
        else:
            f.write("Nenhum padrão de vulnerabilidade óbvio encontrado nas URLs.\n")
        f.write("\n---\n")
        
        # Sumário da Fase 3
        f.write("## Sumário da Fase 3: Scan de Vulnerabilidades (Nuclei)\n")
        if dados_fase3["nuclei_findings"]:
            f.write(f"O Nuclei encontrou **{len(dados_fase3['nuclei_findings'])}** potenciais vulnerabilidades:\n")
            for finding in dados_fase3["nuclei_findings"][:20]: # Limita a 20 para o relatório
                f.write(f"* `{finding}`\n")
            if len(dados_fase3["nuclei_findings"]) > 20: f.write("* ... e mais (ver `nuclei_vulnerabilidades.txt` para a lista completa).*\n")
        else:
            f.write("Nenhuma vulnerabilidade encontrada pelo scan padrão do Nuclei.\n")

    print_sucesso(f"Relatório Final gerado: {os.path.basename(path_relatorio_md)}")

    # --- Limpeza Final ---
    if LIMPAR_ARQUIVOS_INTERMEDIARIOS:
        print_info("Limpando arquivos intermediários...")
        arquivos_para_apagar = [item for item in os.listdir(diretorio_saida) if not item.endswith((".md", ".json"))]
        for arquivo in arquivos_para_apagar:
            try:
                os.remove(f"{diretorio_saida}/{arquivo}")
            except OSError as e:
                print_aviso(f"Não foi possível apagar o arquivo {arquivo}: {e}")
        print_sucesso(f"{len(arquivos_para_apagar)} arquivos intermediários removidos.")
