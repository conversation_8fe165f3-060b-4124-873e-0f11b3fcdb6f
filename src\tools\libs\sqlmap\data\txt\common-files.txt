# Copyright (c) 2006-2025 sqlmap developers (https://sqlmap.org)
# See the file 'LICENSE' for copying permission

# CTFs

/flag
/flag.txt
/readflag

# Reference: https://gist.github.com/sckalath/78ad449346171d29241a

/apache/logs/access.log
/apache/logs/error.log
/bin/php.ini
/etc/alias
/etc/apache2/apache.conf
/etc/apache2/conf/httpd.conf
/etc/apache2/httpd.conf
/etc/apache/conf/httpd.conf
/etc/bash.bashrc
/etc/chttp.conf
/etc/crontab
/etc/crypttab
/etc/debian_version
/etc/exports
/etc/fedora-release
/etc/fstab
/etc/ftphosts
/etc/ftpusers
/etc/group
/etc/group-
/etc/hosts
/etc/http/conf/httpd.conf
/etc/httpd.conf
/etc/httpd/conf/httpd.conf
/etc/httpd/httpd.conf
/etc/httpd/logs/acces_log
/etc/httpd/logs/acces.log
/etc/httpd/logs/access_log
/etc/httpd/logs/access.log
/etc/httpd/logs/error_log
/etc/httpd/logs/error.log
/etc/httpd/php.ini
/etc/http/httpd.conf
/etc/inetd.conf
/etc/inittab
/etc/issue
/etc/issue.net
/etc/lighttpd.conf
/etc/login.defs
/etc/mandrake-release
/etc/motd
/etc/mtab
/etc/my.cnf
/etc/mysql/my.cnf
/etc/openldap/ldap.conf
/etc/os-release
/etc/pam.conf
/etc/passwd
/etc/passwd-
/etc/password.master
/etc/php4.4/fcgi/php.ini
/etc/php4/apache2/php.ini
/etc/php4/apache/php.ini
/etc/php4/cgi/php.ini
/etc/php5/apache2/php.ini
/etc/php5/apache/php.ini
/etc/php5/cgi/php.ini
/etc/php/apache2/php.ini
/etc/php/apache/php.ini
/etc/php/cgi/php.ini
/etc/php.ini
/etc/php/php4/php.ini
/etc/php/php.ini
/etc/profile
/etc/proftp.conf
/etc/proftpd/modules.conf
/etc/protpd/proftpd.conf
/etc/pure-ftpd.conf
/etc/pureftpd.passwd
/etc/pureftpd.pdb
/etc/pure-ftpd/pure-ftpd.conf
/etc/pure-ftpd/pure-ftpd.pdb
/etc/pure-ftpd/pureftpd.pdb
/etc/redhat-release
/etc/resolv.conf
/etc/samba/smb.conf
/etc/security/environ
/etc/security/group
/etc/security/limits
/etc/security/passwd
/etc/security/user
/etc/shadow
/etc/shadow-
/etc/slackware-release
/etc/sudoers
/etc/SUSE-release
/etc/sysctl.conf
/etc/vhcs2/proftpd/proftpd.conf
/etc/vsftpd.conf
/etc/vsftpd/vsftpd.conf
/etc/wu-ftpd/ftpaccess
/etc/wu-ftpd/ftphosts
/etc/wu-ftpd/ftpusers
/logs/access.log
/logs/error.log
/opt/apache2/conf/httpd.conf
/opt/apache/conf/httpd.conf
/opt/xampp/etc/php.ini
/private/etc/httpd/httpd.conf
/private/etc/httpd/httpd.conf.default
/root/.bash_history
/root/.ssh/id_rsa
/root/.ssh/id_rsa.pub
/root/.ssh/known_hosts
/tmp/access.log
/usr/apache2/conf/httpd.conf
/usr/apache/conf/httpd.conf
/usr/etc/pure-ftpd.conf
/usr/lib/php.ini
/usr/lib/php/php.ini
/usr/lib/security/mkuser.default
/usr/local/apache2/conf/httpd.conf
/usr/local/apache2/httpd.conf
/usr/local/apache2/logs/access_log
/usr/local/apache2/logs/access.log
/usr/local/apache2/logs/error_log
/usr/local/apache2/logs/error.log
/usr/local/apache/conf/httpd.conf
/usr/local/apache/conf/php.ini
/usr/local/apache/httpd.conf
/usr/local/apache/logs/access_log
/usr/local/apache/logs/access.log
/usr/local/apache/logs/error_log
/usr/local/apache/logs/error.log
/usr/local/apache/logs/error. og
/usr/local/apps/apache2/conf/httpd.conf
/usr/local/apps/apache/conf/httpd.conf
/usr/local/etc/apache2/conf/httpd.conf
/usr/local/etc/apache/conf/httpd.conf
/usr/local/etc/apache/vhosts.conf
/usr/local/etc/httpd/conf/httpd.conf
/usr/local/etc/php.ini
/usr/local/etc/pure-ftpd.conf
/usr/local/etc/pureftpd.pdb
/usr/local/httpd/conf/httpd.conf
/usr/local/lib/php.ini
/usr/local/php4/httpd.conf
/usr/local/php4/httpd.conf.php
/usr/local/php4/lib/php.ini
/usr/local/php5/httpd.conf
/usr/local/php5/httpd.conf.php
/usr/local/php5/lib/php.ini
/usr/local/php/httpd.conf
/usr/local/php/httpd.conf.php
/usr/local/php/lib/php.ini
/usr/local/pureftpd/etc/pure-ftpd.conf
/usr/local/pureftpd/etc/pureftpd.pdb
/usr/local/pureftpd/sbin/pure-config.pl
/usr/local/Zend/etc/php.ini
/usr/sbin/pure-config.pl
/var/cpanel/cpanel.config
/var/lib/mysql/my.cnf
/var/local/www/conf/php.ini
/var/log/access_log
/var/log/access.log
/var/log/apache2/access_log
/var/log/apache2/access.log
/var/log/apache2/error_log
/var/log/apache2/error.log
/var/log/apache/access_log
/var/log/apache/access.log
/var/log/apache/error_log
/var/log/apache/error.log
/var/log/error_log
/var/log/error.log
/var/log/httpd/access_log
/var/log/httpd/access.log
/var/log/httpd/error_log
/var/log/httpd/error.log
/var/log/messages
/var/log/messages.1
/var/log/user.log
/var/log/user.log.1
/var/www/conf/httpd.conf
/var/www/html/index.html
/var/www/logs/access_log
/var/www/logs/access.log
/var/www/logs/error_log
/var/www/logs/error.log
/Volumes/webBackup/opt/apache2/conf/httpd.conf
/Volumes/webBackup/private/etc/httpd/httpd.conf
/Volumes/webBackup/private/etc/httpd/httpd.conf.default
/web/conf/php.ini

# Reference: https://github.com/devcoinfet/Sqlmap_file_reader/blob/master/file_read.py

/var/log/mysqld.log
/var/www/index.php

# Reference: https://github.com/sqlmapproject/sqlmap/blob/master/lib/core/settings.py#L809-L810

/var/www/index.php
/usr/local/apache/index.php
/usr/local/apache2/index.php
/usr/local/www/apache22/index.php
/usr/local/www/apache24/index.php
/usr/local/httpd/index.php
/var/www/nginx-default/index.php
/srv/www/index.php

/var/www/config.php
/usr/local/apache/config.php
/usr/local/apache2/config.php
/usr/local/www/apache22/config.php
/usr/local/www/apache24/config.php
/usr/local/httpd/config.php
/var/www/nginx-default/config.php
/srv/www/config.php

# Reference: https://github.com/sqlmapproject/sqlmap/issues/3928

/srv/www/htdocs/index.php
/usr/local/apache2/htdocs/index.php
/usr/local/www/data/index.php
/var/apache2/htdocs/index.php
/var/www/htdocs/index.php
/var/www/html/index.php

/srv/www/htdocs/config.php
/usr/local/apache2/htdocs/config.php
/usr/local/www/data/config.php
/var/apache2/htdocs/config.php
/var/www/htdocs/config.php
/var/www/html/config.php

# Reference: https://www.gracefulsecurity.com/path-traversal-cheat-sheet-linux

/etc/passwd
/etc/shadow
/etc/aliases
/etc/anacrontab
/etc/apache2/apache2.conf
/etc/apache2/httpd.conf
/etc/at.allow
/etc/at.deny
/etc/bashrc
/etc/bootptab
/etc/chrootUsers
/etc/chttp.conf
/etc/cron.allow
/etc/cron.deny
/etc/crontab
/etc/cups/cupsd.conf
/etc/exports
/etc/fstab
/etc/ftpaccess
/etc/ftpchroot
/etc/ftphosts
/etc/groups
/etc/grub.conf
/etc/hosts
/etc/hosts.allow
/etc/hosts.deny
/etc/httpd/access.conf
/etc/httpd/conf/httpd.conf
/etc/httpd/httpd.conf
/etc/httpd/logs/access_log
/etc/httpd/logs/access.log
/etc/httpd/logs/error_log
/etc/httpd/logs/error.log
/etc/httpd/php.ini
/etc/httpd/srm.conf
/etc/inetd.conf
/etc/inittab
/etc/issue
/etc/lighttpd.conf
/etc/lilo.conf
/etc/logrotate.d/ftp
/etc/logrotate.d/proftpd
/etc/logrotate.d/vsftpd.log
/etc/lsb-release
/etc/motd
/etc/modules.conf
/etc/motd
/etc/mtab
/etc/my.cnf
/etc/my.conf
/etc/mysql/my.cnf
/etc/network/interfaces
/etc/networks
/etc/npasswd
/etc/passwd
/etc/php4.4/fcgi/php.ini
/etc/php4/apache2/php.ini
/etc/php4/apache/php.ini
/etc/php4/cgi/php.ini
/etc/php4/apache2/php.ini
/etc/php5/apache2/php.ini
/etc/php5/apache/php.ini
/etc/php/apache2/php.ini
/etc/php/apache/php.ini
/etc/php/cgi/php.ini
/etc/php.ini
/etc/php/php4/php.ini
/etc/php/php.ini
/etc/printcap
/etc/profile
/etc/proftp.conf
/etc/proftpd/proftpd.conf
/etc/pure-ftpd.conf
/etc/pureftpd.passwd
/etc/pureftpd.pdb
/etc/pure-ftpd/pure-ftpd.conf
/etc/pure-ftpd/pure-ftpd.pdb
/etc/pure-ftpd/putreftpd.pdb
/etc/redhat-release
/etc/resolv.conf
/etc/samba/smb.conf
/etc/snmpd.conf
/etc/ssh/ssh_config
/etc/ssh/sshd_config
/etc/ssh/ssh_host_dsa_key
/etc/ssh/ssh_host_dsa_key.pub
/etc/ssh/ssh_host_key
/etc/ssh/ssh_host_key.pub
/etc/sysconfig/network
/etc/syslog.conf
/etc/termcap
/etc/vhcs2/proftpd/proftpd.conf
/etc/vsftpd.chroot_list
/etc/vsftpd.conf
/etc/vsftpd/vsftpd.conf
/etc/wu-ftpd/ftpaccess
/etc/wu-ftpd/ftphosts
/etc/wu-ftpd/ftpusers
/logs/pure-ftpd.log
/logs/security_debug_log
/logs/security_log
/opt/lampp/etc/httpd.conf
/opt/xampp/etc/php.ini
/proc/cpuinfo
/proc/filesystems
/proc/interrupts
/proc/ioports
/proc/meminfo
/proc/modules
/proc/mounts
/proc/stat
/proc/swaps
/proc/version
/proc/self/net/arp
/root/anaconda-ks.cfg
/usr/etc/pure-ftpd.conf
/usr/lib/php.ini
/usr/lib/php/php.ini
/usr/local/apache/conf/modsec.conf
/usr/local/apache/conf/php.ini
/usr/local/apache/log
/usr/local/apache/logs
/usr/local/apache/logs/access_log
/usr/local/apache/logs/access.log
/usr/local/apache/audit_log
/usr/local/apache/error_log
/usr/local/apache/error.log
/usr/local/cpanel/logs
/usr/local/cpanel/logs/access_log
/usr/local/cpanel/logs/error_log
/usr/local/cpanel/logs/license_log
/usr/local/cpanel/logs/login_log
/usr/local/cpanel/logs/stats_log
/usr/local/etc/httpd/logs/access_log
/usr/local/etc/httpd/logs/error_log
/usr/local/etc/php.ini
/usr/local/etc/pure-ftpd.conf
/usr/local/etc/pureftpd.pdb
/usr/local/lib/php.ini
/usr/local/php4/httpd.conf
/usr/local/php4/httpd.conf.php
/usr/local/php4/lib/php.ini
/usr/local/php5/httpd.conf
/usr/local/php5/httpd.conf.php
/usr/local/php5/lib/php.ini
/usr/local/php/httpd.conf
/usr/local/php/httpd.conf.ini
/usr/local/php/lib/php.ini
/usr/local/pureftpd/etc/pure-ftpd.conf
/usr/local/pureftpd/etc/pureftpd.pdn
/usr/local/pureftpd/sbin/pure-config.pl
/usr/local/www/logs/httpd_log
/usr/local/Zend/etc/php.ini
/usr/sbin/pure-config.pl
/var/adm/log/xferlog
/var/apache2/config.inc
/var/apache/logs/access_log
/var/apache/logs/error_log
/var/cpanel/cpanel.config
/var/lib/mysql/my.cnf
/var/lib/mysql/mysql/user.MYD
/var/local/www/conf/php.ini
/var/log/apache2/access_log
/var/log/apache2/access.log
/var/log/apache2/error_log
/var/log/apache2/error.log
/var/log/apache/access_log
/var/log/apache/access.log
/var/log/apache/error_log
/var/log/apache/error.log
/var/log/apache-ssl/access.log
/var/log/apache-ssl/error.log
/var/log/auth.log
/var/log/boot
/var/htmp
/var/log/chttp.log
/var/log/cups/error.log
/var/log/daemon.log
/var/log/debug
/var/log/dmesg
/var/log/dpkg.log
/var/log/exim_mainlog
/var/log/exim/mainlog
/var/log/exim_paniclog
/var/log/exim.paniclog
/var/log/exim_rejectlog
/var/log/exim/rejectlog
/var/log/faillog
/var/log/ftplog
/var/log/ftp-proxy
/var/log/ftp-proxy/ftp-proxy.log
/var/log/httpd/access_log
/var/log/httpd/access.log
/var/log/httpd/error_log
/var/log/httpd/error.log
/var/log/httpsd/ssl.access_log
/var/log/httpsd/ssl_log
/var/log/kern.log
/var/log/lastlog
/var/log/lighttpd/access.log
/var/log/lighttpd/error.log
/var/log/lighttpd/lighttpd.access.log
/var/log/lighttpd/lighttpd.error.log
/var/log/mail.info
/var/log/mail.log
/var/log/maillog
/var/log/mail.warn
/var/log/message
/var/log/messages
/var/log/mysqlderror.log
/var/log/mysql.log
/var/log/mysql/mysql-bin.log
/var/log/mysql/mysql.log
/var/log/mysql/mysql-slow.log
/var/log/proftpd
/var/log/pureftpd.log
/var/log/pure-ftpd/pure-ftpd.log
/var/log/secure
/var/log/vsftpd.log
/var/log/wtmp
/var/log/xferlog
/var/log/yum.log
/var/mysql.log
/var/run/utmp
/var/spool/cron/crontabs/root
/var/webmin/miniserv.log
/var/www/log/access_log
/var/www/log/error_log
/var/www/logs/access_log
/var/www/logs/error_log
/var/www/logs/access.log
/var/www/logs/error.log

# Reference: https://nets.ec/File_Inclusion

/etc/passwd
/etc/master.passwd
/etc/shadow
/var/db/shadow/hash
/etc/group
/etc/hosts
/etc/motd
/etc/issue
/etc/release
/etc/redhat-release
/etc/crontab
/etc/inittab
/proc/version
/proc/cmdline
/proc/self/environ
/proc/self/fd/0
/proc/self/fd/1
/proc/self/fd/2
/proc/self/fd/255
/etc/httpd.conf
/etc/apache2.conf
/etc/apache2/apache2.conf
/etc/apache2/httpd.conf
/etc/httpd/conf/httpd.conf
/etc/httpd/httpd.conf
/etc/apache2/conf/httpd.conf
/etc/apache/conf/httpd.conf
/usr/local/apache2/conf/httpd.conf
/usr/local/apache/conf/httpd.conf
/etc/apache2/sites-enabled/000-default
/etc/apache2/sites-available/default
/etc/nginx.conf
/etc/nginx/nginx.conf
/etc/nginx/sites-available/default
/etc/nginx/sites-enabled/default
/etc/ssh/sshd_config
/etc/my.cnf
/etc/mysql/my.cnf
/etc/php.ini
/var/mail/www-data
/var/mail/www
/var/mail/apache
/var/mail/nobody
/var/www/.bash_history
/root/.bash_history
/var/root/.bash_history
/var/root/.sh_history
/etc/passwd
/etc/master.passwd
/etc/shadow
/var/db/shadow/hash
/etc/group
/etc/hosts
/etc/motd
/etc/issue
/etc/release
/etc/redhat-release
/etc/crontab
/etc/inittab
/proc/version
/proc/cmdline
/proc/self/environ
/proc/self/fd/0
/proc/self/fd/1
/proc/self/fd/2
/proc/self/fd/255
/etc/httpd.conf
/etc/apache2.conf
/etc/apache2/apache2.conf
/etc/apache2/httpd.conf
/etc/httpd/conf/httpd.conf
/etc/httpd/httpd.conf
/etc/apache2/conf/httpd.conf
/etc/apache/conf/httpd.conf
/usr/local/apache2/conf/httpd.conf
/usr/local/apache/conf/httpd.conf
/etc/apache2/sites-enabled/000-default
/etc/apache2/sites-available/default
/etc/nginx.conf
/etc/nginx/nginx.conf
/etc/nginx/sites-available/default
/etc/nginx/sites-enabled/default
/etc/ssh/sshd_config
/etc/my.cnf
/etc/mysql/my.cnf
/etc/php.ini
/var/mail/www-data
/var/mail/www
/var/mail/apache
/var/mail/nobody
/var/www/.bash_history
/root/.bash_history
/var/root/.bash_history
/var/root/.sh_history
/usr/local/apache/httpd.conf
/usr/local/apache2/httpd.conf
/usr/local/httpd/conf/httpd.conf
/usr/local/etc/apache/conf/httpd.conf
/usr/local/etc/apache2/conf/httpd.conf
/usr/local/etc/httpd/conf/httpd.conf
/usr/apache2/conf/httpd.conf
/usr/apache/conf/httpd.conf
/etc/http/conf/httpd.conf
/etc/http/httpd.conf
/opt/apache/conf/httpd.conf
/opt/apache2/conf/httpd.conf
/var/www/conf/httpd.conf
/usr/local/php/httpd.conf
/usr/local/php4/httpd.conf
/usr/local/php5/httpd.conf
/etc/httpd/php.ini
/usr/lib/php.ini
/usr/lib/php/php.ini
/usr/local/etc/php.ini
/usr/local/lib/php.ini
/usr/local/php/lib/php.ini
/usr/local/php4/lib/php.ini
/usr/local/php5/lib/php.ini
/usr/local/apache/conf/php.ini
/etc/php4/apache/php.ini
/etc/php4/apache2/php.ini
/etc/php5/apache/php.ini
/etc/php5/apache2/php.ini
/etc/php/php.ini
/etc/php/php4/php.ini
/etc/php/apache/php.ini
/etc/php/apache2/php.ini
/usr/local/Zend/etc/php.ini
/opt/xampp/etc/php.ini
/var/local/www/conf/php.ini
/etc/php/cgi/php.ini
/etc/php4/cgi/php.ini
/etc/php5/cgi/php.ini
/var/log/lastlog
/var/log/wtmp
/var/run/utmp
/var/log/messages.log
/var/log/messages
/var/log/messages.0
/var/log/messages.1
/var/log/messages.2
/var/log/messages.3
/var/log/syslog.log
/var/log/syslog
/var/log/syslog.0
/var/log/syslog.1
/var/log/syslog.2
/var/log/syslog.3
/var/log/auth.log
/var/log/auth.log.0
/var/log/auth.log.1
/var/log/auth.log.2
/var/log/auth.log.3
/var/log/authlog
/var/log/syslog
/var/adm/lastlog
/var/adm/messages
/var/adm/messages.0
/var/adm/messages.1
/var/adm/messages.2
/var/adm/messages.3
/var/adm/utmpx
/var/adm/wtmpx
/var/log/kernel.log
/var/log/secure.log
/var/log/mail.log
/var/run/utmp
/var/log/wtmp
/var/log/lastlog
/var/log/access.log
/var/log/access_log
/var/log/error.log
/var/log/error_log
/var/log/apache2/access.log
/var/log/apache2/access_log
/var/log/apache2/error.log
/var/log/apache2/error_log
/var/log/apache/access.log
/var/log/apache/access_log
/var/log/apache/error.log
/var/log/apache/error_log
/var/log/httpd/access.log
/var/log/httpd/access_log
/var/log/httpd/error.log
/var/log/httpd/error_log
/etc/httpd/logs/access.log
/etc/httpd/logs/access_log
/etc/httpd/logs/error.log
/etc/httpd/logs/error_log
/usr/local/apache/logs/access.log
/usr/local/apache/logs/access_log
/usr/local/apache/logs/error.log
/usr/local/apache/logs/error_log
/usr/local/apache2/logs/access.log
/usr/local/apache2/logs/access_log
/usr/local/apache2/logs/error.log
/usr/local/apache2/logs/error_log
/var/www/logs/access.log
/var/www/logs/access_log
/var/www/logs/error.log
/var/www/logs/error_log
/opt/lampp/logs/access.log
/opt/lampp/logs/access_log
/opt/lampp/logs/error.log
/opt/lampp/logs/error_log
/opt/xampp/logs/access.log
/opt/xampp/logs/access_log
/opt/xampp/logs/error.log
/opt/xampp/logs/error_log

# Reference: https://github.com/ironbee/ironbee-rules/blob/master/rules/lfi-files.data

/.htaccess
/.htpasswd
/access.log
/access_log
/apache/conf/httpd.conf
/apache/logs/access.log
/apache/logs/error.log
/apache/php/php.ini
/apache2/logs/access.log
/apache2/logs/error.log
/bin/php.ini
/boot.ini
/boot/grub/grub.cfg
/boot/grub/menu.lst
/config.inc.php
/error.log
/error_log
/etc/adduser.conf
/etc/alias
/etc/apache/access.conf
/etc/apache/apache.conf
/etc/apache/conf/httpd.conf
/etc/apache/default-server.conf
/etc/apache/httpd.conf
/etc/apache2/apache.conf
/etc/apache2/apache2.conf
/etc/apache2/conf.d/charset
/etc/apache2/conf.d/phpmyadmin.conf
/etc/apache2/conf.d/security
/etc/apache2/conf/httpd.conf
/etc/apache2/default-server.conf
/etc/apache2/envvars
/etc/apache2/httpd.conf
/etc/apache2/httpd2.conf
/etc/apache2/mods-available/autoindex.conf
/etc/apache2/mods-available/deflate.conf
/etc/apache2/mods-available/dir.conf
/etc/apache2/mods-available/mem_cache.conf
/etc/apache2/mods-available/mime.conf
/etc/apache2/mods-available/proxy.conf
/etc/apache2/mods-available/setenvif.conf
/etc/apache2/mods-available/ssl.conf
/etc/apache2/mods-enabled/alias.conf
/etc/apache2/mods-enabled/deflate.conf
/etc/apache2/mods-enabled/dir.conf
/etc/apache2/mods-enabled/mime.conf
/etc/apache2/mods-enabled/negotiation.conf
/etc/apache2/mods-enabled/php5.conf
/etc/apache2/mods-enabled/status.conf
/etc/apache2/ports.conf
/etc/apache2/sites-available/default
/etc/apache2/sites-available/default-ssl
/etc/apache2/sites-enabled/000-default
/etc/apache2/sites-enabled/default
/etc/apache2/ssl-global.conf
/etc/apache2/vhosts.d/00_default_vhost.conf
/etc/apache2/vhosts.d/default_vhost.include
/etc/apache22/conf/httpd.conf
/etc/apache22/httpd.conf
/etc/apt/apt.conf
/etc/avahi/avahi-daemon.conf
/etc/bash.bashrc
/etc/bash_completion.d/debconf
/etc/bluetooth/input.conf
/etc/bluetooth/main.conf
/etc/bluetooth/network.conf
/etc/bluetooth/rfcomm.conf
/etc/ca-certificates.conf
/etc/ca-certificates.conf.dpkg-old
/etc/casper.conf
/etc/chkrootkit.conf
/etc/chrootusers
/etc/clamav/clamd.conf
/etc/clamav/freshclam.conf
/etc/crontab
/etc/crypttab
/etc/cups/acroread.conf
/etc/cups/cupsd.conf
/etc/cups/cupsd.conf.default
/etc/cups/pdftops.conf
/etc/cups/printers.conf
/etc/cvs-cron.conf
/etc/cvs-pserver.conf
/etc/debconf.conf
/etc/debian_version
/etc/default/grub
/etc/deluser.conf
/etc/dhcp/dhclient.conf
/etc/dhcp3/dhclient.conf
/etc/dhcp3/dhcpd.conf
/etc/dns2tcpd.conf
/etc/e2fsck.conf
/etc/esound/esd.conf
/etc/etter.conf
/etc/exports
/etc/fedora-release
/etc/firewall.rules
/etc/foremost.conf
/etc/fstab
/etc/ftpchroot
/etc/ftphosts
/etc/ftpusers
/etc/fuse.conf
/etc/group
/etc/group-
/etc/hdparm.conf
/etc/host.conf
/etc/hostname
/etc/hosts
/etc/hosts.allow
/etc/hosts.deny
/etc/http/conf/httpd.conf
/etc/http/httpd.conf
/etc/httpd.conf
/etc/httpd/apache.conf
/etc/httpd/apache2.conf
/etc/httpd/conf
/etc/httpd/conf.d
/etc/httpd/conf.d/php.conf
/etc/httpd/conf.d/squirrelmail.conf
/etc/httpd/conf/apache.conf
/etc/httpd/conf/apache2.conf
/etc/httpd/conf/httpd.conf
/etc/httpd/extra/httpd-ssl.conf
/etc/httpd/httpd.conf
/etc/httpd/logs/access.log
/etc/httpd/logs/access_log
/etc/httpd/logs/error.log
/etc/httpd/logs/error_log
/etc/httpd/mod_php.conf
/etc/httpd/php.ini
/etc/inetd.conf
/etc/init.d
/etc/inittab
/etc/ipfw.conf
/etc/ipfw.rules
/etc/issue
/etc/issue
/etc/issue.net
/etc/kbd/config
/etc/kernel-img.conf
/etc/kernel-pkg.conf
/etc/ld.so.conf
/etc/ldap/ldap.conf
/etc/lighttpd/lighthttpd.conf
/etc/login.defs
/etc/logrotate.conf
/etc/logrotate.d/ftp
/etc/logrotate.d/proftpd
/etc/logrotate.d/vsftpd.log
/etc/ltrace.conf
/etc/mail/sendmail.conf
/etc/mandrake-release
/etc/manpath.config
/etc/miredo-server.conf
/etc/miredo.conf
/etc/miredo/miredo-server.conf
/etc/miredo/miredo.conf
/etc/modprobe.d/vmware-tools.conf
/etc/modules
/etc/mono/1.0/machine.config
/etc/mono/2.0/machine.config
/etc/mono/2.0/web.config
/etc/mono/config
/etc/motd
/etc/motd
/etc/mtab
/etc/mtools.conf
/etc/muddleftpd.com
/etc/muddleftpd/muddleftpd.conf
/etc/muddleftpd/muddleftpd.passwd
/etc/muddleftpd/mudlog
/etc/muddleftpd/mudlogd.conf
/etc/muddleftpd/passwd
/etc/my.cnf
/etc/mysql/conf.d/old_passwords.cnf
/etc/mysql/my.cnf
/etc/networks
/etc/newsyslog.conf
/etc/nginx/nginx.conf
/etc/openldap/ldap.conf
/etc/os-release
/etc/osxhttpd/osxhttpd.conf
/etc/pam.conf
/etc/pam.d/proftpd
/etc/passwd
/etc/passwd
/etc/passwd-
/etc/passwd~
/etc/password.master
/etc/php.ini
/etc/php/apache/php.ini
/etc/php/apache2/php.ini
/etc/php/cgi/php.ini
/etc/php/php.ini
/etc/php/php4/php.ini
/etc/php4.4/fcgi/php.ini
/etc/php4/apache/php.ini
/etc/php4/apache2/php.ini
/etc/php4/cgi/php.ini
/etc/php5/apache/php.ini
/etc/php5/apache2/php.ini
/etc/php5/cgi/php.ini
/etc/phpmyadmin/config.inc.php
/etc/postgresql/pg_hba.conf
/etc/postgresql/postgresql.conf
/etc/profile
/etc/proftp.conf
/etc/proftpd/modules.conf
/etc/protpd/proftpd.conf
/etc/pulse/client.conf
/etc/pure-ftpd.conf
/etc/pure-ftpd/pure-ftpd.conf
/etc/pure-ftpd/pure-ftpd.pdb
/etc/pure-ftpd/pureftpd.pdb
/etc/pureftpd.passwd
/etc/pureftpd.pdb
/etc/rc.conf
/etc/rc.d/rc.httpd
/etc/redhat-release
/etc/resolv.conf
/etc/resolvconf/update-libc.d/sendmail
/etc/samba/dhcp.conf
/etc/samba/netlogon
/etc/samba/private/smbpasswd
/etc/samba/samba.conf
/etc/samba/smb.conf
/etc/samba/smb.conf.user
/etc/samba/smbpasswd
/etc/samba/smbusers
/etc/security/access.conf
/etc/security/environ
/etc/security/failedlogin
/etc/security/group
/etc/security/group.conf
/etc/security/lastlog
/etc/security/limits
/etc/security/limits.conf
/etc/security/namespace.conf
/etc/security/opasswd
/etc/security/pam_env.conf
/etc/security/passwd
/etc/security/sepermit.conf
/etc/security/time.conf
/etc/security/user
/etc/sensors.conf
/etc/sensors3.conf
/etc/shadow
/etc/shadow-
/etc/shadow~
/etc/slackware-release
/etc/smb.conf
/etc/smbpasswd
/etc/smi.conf
/etc/squirrelmail/apache.conf
/etc/squirrelmail/config.php
/etc/squirrelmail/config/config.php
/etc/squirrelmail/config_default.php
/etc/squirrelmail/config_local.php
/etc/squirrelmail/default_pref
/etc/squirrelmail/filters_setup.php
/etc/squirrelmail/index.php
/etc/squirrelmail/sqspell_config.php
/etc/ssh/sshd_config
/etc/sso/sso_config.ini
/etc/stunnel/stunnel.conf
/etc/subversion/config
/etc/sudoers
/etc/suse-release
/etc/sw-cp-server/applications.d/00-sso-cpserver.conf
/etc/sw-cp-server/applications.d/plesk.conf
/etc/sysconfig/network-scripts/ifcfg-eth0
/etc/sysctl.conf
/etc/sysctl.d/10-console-messages.conf
/etc/sysctl.d/10-network-security.conf
/etc/sysctl.d/10-process-security.conf
/etc/sysctl.d/wine.sysctl.conf
/etc/syslog.conf
/etc/timezone
/etc/tinyproxy/tinyproxy.conf
/etc/tor/tor-tsocks.conf
/etc/tsocks.conf
/etc/updatedb.conf
/etc/updatedb.conf.beforevmwaretoolsinstall
/etc/utmp
/etc/vhcs2/proftpd/proftpd.conf
/etc/vmware-tools/config
/etc/vmware-tools/tpvmlp.conf
/etc/vmware-tools/vmware-tools-libraries.conf
/etc/vsftpd.chroot_list
/etc/vsftpd.conf
/etc/vsftpd/vsftpd.conf
/etc/webmin/miniserv.conf
/etc/webmin/miniserv.users
/etc/wicd/dhclient.conf.template.default
/etc/wicd/manager-settings.conf
/etc/wicd/wired-settings.conf
/etc/wicd/wireless-settings.conf
/etc/wu-ftpd/ftpaccess
/etc/wu-ftpd/ftphosts
/etc/wu-ftpd/ftpusers
/etc/x11/xorg.conf
/etc/x11/xorg.conf-vesa
/etc/x11/xorg.conf-vmware
/etc/x11/xorg.conf.beforevmwaretoolsinstall
/etc/x11/xorg.conf.orig
/home/<USER>/stable/apache/php.ini
/home/<USER>/data/pg_hba.conf
/home/<USER>/data/pg_ident.conf
/home/<USER>/data/pg_version
/home/<USER>/data/postgresql.conf
/home/<USER>/lighttpd/lighttpd.conf
/home2/bin/stable/apache/php.ini
/http/httpd.conf
/library/webserver/documents/.htaccess
/library/webserver/documents/default.htm
/library/webserver/documents/default.html
/library/webserver/documents/default.php
/library/webserver/documents/index.htm
/library/webserver/documents/index.html
/library/webserver/documents/index.php
/logs/access.log
/logs/access_log
/logs/error.log
/logs/error_log
/logs/pure-ftpd.log
/logs/security_debug_log
/logs/security_log
/mysql/bin/my.ini
/mysql/data/mysql-bin.index
/mysql/data/mysql-bin.log
/mysql/data/mysql.err
/mysql/data/mysql.log
/mysql/my.cnf
/mysql/my.ini
/netserver/bin/stable/apache/php.ini
/opt/jboss/server/default/conf/jboss-minimal.xml
/opt/jboss/server/default/conf/jboss-service.xml
/opt/jboss/server/default/conf/jndi.properties
/opt/jboss/server/default/conf/log4j.xml
/opt/jboss/server/default/conf/login-config.xml
/opt/jboss/server/default/conf/server.log.properties
/opt/jboss/server/default/conf/standardjaws.xml
/opt/jboss/server/default/conf/standardjboss.xml
/opt/jboss/server/default/deploy/jboss-logging.xml
/opt/jboss/server/default/log/boot.log
/opt/jboss/server/default/log/server.log
/opt/apache/apache.conf
/opt/apache/apache2.conf
/opt/apache/conf/apache.conf
/opt/apache/conf/apache2.conf
/opt/apache/conf/httpd.conf
/opt/apache2/apache.conf
/opt/apache2/apache2.conf
/opt/apache2/conf/apache.conf
/opt/apache2/conf/apache2.conf
/opt/apache2/conf/httpd.conf
/opt/apache22/conf/httpd.conf
/opt/httpd/apache.conf
/opt/httpd/apache2.conf
/opt/httpd/conf/apache.conf
/opt/httpd/conf/apache2.conf
/opt/lampp/etc/httpd.conf
/opt/lampp/logs/access.log
/opt/lampp/logs/access_log
/opt/lampp/logs/error.log
/opt/lampp/logs/error_log
/opt/lsws/conf/httpd_conf.xml
/opt/lsws/logs/access.log
/opt/lsws/logs/error.log
/opt/tomcat/logs/catalina.err
/opt/tomcat/logs/catalina.out
/opt/xampp/etc/php.ini
/opt/xampp/logs/access.log
/opt/xampp/logs/access_log
/opt/xampp/logs/error.log
/opt/xampp/logs/error_log
/php/php.ini
/php/php.ini
/php4/php.ini
/php5/php.ini
/postgresql/log/pgadmin.log
/private/etc/httpd/apache.conf
/private/etc/httpd/apache2.conf
/private/etc/httpd/httpd.conf
/private/etc/httpd/httpd.conf.default
/private/etc/squirrelmail/config/config.php
/proc/cpuinfo
/proc/devices
/proc/meminfo
/proc/net/tcp
/proc/net/udp
/proc/self/cmdline
/proc/self/environ
/proc/self/environ
/proc/self/fd/0
/proc/self/fd/1
/proc/self/fd/10
/proc/self/fd/11
/proc/self/fd/12
/proc/self/fd/13
/proc/self/fd/14
/proc/self/fd/15
/proc/self/fd/2
/proc/self/fd/3
/proc/self/fd/4
/proc/self/fd/5
/proc/self/fd/6
/proc/self/fd/7
/proc/self/fd/8
/proc/self/fd/9
/proc/self/mounts
/proc/self/stat
/proc/self/status
/proc/version
/program files/jboss/server/default/conf/jboss-minimal.xml
/program files/jboss/server/default/conf/jboss-service.xml
/program files/jboss/server/default/conf/jndi.properties
/program files/jboss/server/default/conf/log4j.xml
/program files/jboss/server/default/conf/login-config.xml
/program files/jboss/server/default/conf/server.log.properties
/program files/jboss/server/default/conf/standardjaws.xml
/program files/jboss/server/default/conf/standardjboss.xml
/program files/jboss/server/default/deploy/jboss-logging.xml
/program files/jboss/server/default/log/boot.log
/program files/jboss/server/default/log/server.log
/program files/apache group/apache/apache.conf
/program files/apache group/apache/apache2.conf
/program files/apache group/apache/conf/apache.conf
/program files/apache group/apache/conf/apache2.conf
/program files/apache group/apache/conf/httpd.conf
/program files/apache group/apache/logs/access.log
/program files/apache group/apache/logs/error.log
/program files/apache group/apache2/conf/apache.conf
/program files/apache group/apache2/conf/apache2.conf
/program files/apache group/apache2/conf/httpd.conf
/program files/apache software foundation/apache2.2/conf/httpd.conf
/program files/apache software foundation/apache2.2/logs/access.log
/program files/apache software foundation/apache2.2/logs/error.log
/program files/mysql/data/mysql-bin.index
/program files/mysql/data/mysql-bin.log
/program files/mysql/data/mysql.err
/program files/mysql/data/mysql.log
/program files/mysql/my.cnf
/program files/mysql/my.ini
/program files/mysql/mysql server 5.0/data/mysql-bin.index
/program files/mysql/mysql server 5.0/data/mysql-bin.log
/program files/mysql/mysql server 5.0/data/mysql.err
/program files/mysql/mysql server 5.0/data/mysql.log
/program files/mysql/mysql server 5.0/my.cnf
/program files/mysql/mysql server 5.0/my.ini
/program files/postgresql/8.3/data/pg_hba.conf
/program files/postgresql/8.3/data/pg_ident.conf
/program files/postgresql/8.3/data/postgresql.conf
/program files/postgresql/8.4/data/pg_hba.conf
/program files/postgresql/8.4/data/pg_ident.conf
/program files/postgresql/8.4/data/postgresql.conf
/program files/postgresql/9.0/data/pg_hba.conf
/program files/postgresql/9.0/data/pg_ident.conf
/program files/postgresql/9.0/data/postgresql.conf
/program files/postgresql/9.1/data/pg_hba.conf
/program files/postgresql/9.1/data/pg_ident.conf
/program files/postgresql/9.1/data/postgresql.conf
/program files/vidalia bundle/polipo/polipo.conf
/program files/xampp/apache/conf/apache.conf
/program files/xampp/apache/conf/apache2.conf
/program files/xampp/apache/conf/httpd.conf
/root/.bash_config
/root/.bash_history
/root/.bash_logout
/root/.bashrc
/root/.ksh_history
/root/.xauthority
/srv/www/htdos/squirrelmail/config/config.php
/ssl_request_log        
/system/library/webobjects/adaptors/apache2.2/apache.conf
/temp/sess_
/thttpd_log
/tmp/jboss/server/default/conf/jboss-minimal.xml
/tmp/jboss/server/default/conf/jboss-service.xml
/tmp/jboss/server/default/conf/jndi.properties
/tmp/jboss/server/default/conf/log4j.xml
/tmp/jboss/server/default/conf/login-config.xml
/tmp/jboss/server/default/conf/server.log.properties
/tmp/jboss/server/default/conf/standardjaws.xml
/tmp/jboss/server/default/conf/standardjboss.xml
/tmp/jboss/server/default/deploy/jboss-logging.xml
/tmp/jboss/server/default/log/boot.log
/tmp/jboss/server/default/log/server.log
/tmp/access.log
/tmp/sess_
/usr/apache/conf/httpd.conf
/usr/apache2/conf/httpd.conf
/usr/etc/pure-ftpd.conf
/usr/home/<USER>/lighttpd/lighttpd.conf
/usr/home/<USER>/var/log/apache.log
/usr/home/<USER>/var/log/lighttpd.error.log
/usr/internet/pgsql/data/pg_hba.conf
/usr/internet/pgsql/data/postmaster.log
/usr/lib/cron/log
/usr/lib/php.ini
/usr/lib/php/php.ini
/usr/lib/security/mkuser.default
/usr/local/jboss/server/default/conf/jboss-minimal.xml
/usr/local/jboss/server/default/conf/jboss-service.xml
/usr/local/jboss/server/default/conf/jndi.properties
/usr/local/jboss/server/default/conf/log4j.xml
/usr/local/jboss/server/default/conf/login-config.xml
/usr/local/jboss/server/default/conf/server.log.properties
/usr/local/jboss/server/default/conf/standardjaws.xml
/usr/local/jboss/server/default/conf/standardjboss.xml
/usr/local/jboss/server/default/deploy/jboss-logging.xml
/usr/local/jboss/server/default/log/boot.log
/usr/local/jboss/server/default/log/server.log
/usr/local/apache/apache.conf
/usr/local/apache/apache2.conf
/usr/local/apache/conf/access.conf
/usr/local/apache/conf/apache.conf
/usr/local/apache/conf/apache2.conf
/usr/local/apache/conf/httpd.conf
/usr/local/apache/conf/httpd.conf.default
/usr/local/apache/conf/modsec.conf
/usr/local/apache/conf/php.ini
/usr/local/apache/conf/vhosts-custom.conf
/usr/local/apache/conf/vhosts.conf
/usr/local/apache/httpd.conf
/usr/local/apache/logs/access.log
/usr/local/apache/logs/access_log
/usr/local/apache/logs/audit_log
/usr/local/apache/logs/error.log
/usr/local/apache/logs/error_log
/usr/local/apache/logs/lighttpd.error.log
/usr/local/apache/logs/lighttpd.log
/usr/local/apache/logs/mod_jk.log
/usr/local/apache1.3/conf/httpd.conf
/usr/local/apache2/apache.conf
/usr/local/apache2/apache2.conf
/usr/local/apache2/conf/apache.conf
/usr/local/apache2/conf/apache2.conf
/usr/local/apache2/conf/extra/httpd-ssl.conf
/usr/local/apache2/conf/httpd.conf
/usr/local/apache2/conf/modsec.conf
/usr/local/apache2/conf/ssl.conf
/usr/local/apache2/conf/vhosts-custom.conf
/usr/local/apache2/conf/vhosts.conf
/usr/local/apache2/httpd.conf
/usr/local/apache2/logs/access.log
/usr/local/apache2/logs/access_log
/usr/local/apache2/logs/audit_log
/usr/local/apache2/logs/error.log
/usr/local/apache2/logs/error_log
/usr/local/apache2/logs/lighttpd.error.log
/usr/local/apache2/logs/lighttpd.log
/usr/local/apache22/conf/httpd.conf
/usr/local/apache22/httpd.conf
/usr/local/apps/apache/conf/httpd.conf
/usr/local/apps/apache2/conf/httpd.conf
/usr/local/apps/apache22/conf/httpd.conf
/usr/local/cpanel/logs/access_log
/usr/local/cpanel/logs/error_log
/usr/local/cpanel/logs/license_log
/usr/local/cpanel/logs/login_log
/usr/local/cpanel/logs/stats_log
/usr/local/etc/apache/conf/httpd.conf
/usr/local/etc/apache/httpd.conf
/usr/local/etc/apache/vhosts.conf
/usr/local/etc/apache2/conf/httpd.conf
/usr/local/etc/apache2/httpd.conf
/usr/local/etc/apache2/vhosts.conf
/usr/local/etc/apache22/conf/httpd.conf
/usr/local/etc/apache22/httpd.conf
/usr/local/etc/httpd/conf
/usr/local/etc/httpd/conf/httpd.conf
/usr/local/etc/lighttpd.conf
/usr/local/etc/lighttpd.conf.new
/usr/local/etc/nginx/nginx.conf
/usr/local/etc/php.ini
/usr/local/etc/pure-ftpd.conf
/usr/local/etc/pureftpd.pdb
/usr/local/etc/smb.conf
/usr/local/etc/webmin/miniserv.conf
/usr/local/etc/webmin/miniserv.users
/usr/local/httpd/conf/httpd.conf
/usr/local/jakarta/dist/tomcat/conf/context.xml
/usr/local/jakarta/dist/tomcat/conf/jakarta.conf
/usr/local/jakarta/dist/tomcat/conf/logging.properties
/usr/local/jakarta/dist/tomcat/conf/server.xml
/usr/local/jakarta/dist/tomcat/conf/workers.properties
/usr/local/jakarta/dist/tomcat/logs/mod_jk.log
/usr/local/jakarta/tomcat/conf/context.xml
/usr/local/jakarta/tomcat/conf/jakarta.conf
/usr/local/jakarta/tomcat/conf/logging.properties
/usr/local/jakarta/tomcat/conf/server.xml
/usr/local/jakarta/tomcat/conf/workers.properties
/usr/local/jakarta/tomcat/logs/catalina.err
/usr/local/jakarta/tomcat/logs/catalina.out
/usr/local/jakarta/tomcat/logs/mod_jk.log
/usr/local/lib/php.ini
/usr/local/lighttpd/conf/lighttpd.conf
/usr/local/lighttpd/log/access.log
/usr/local/lighttpd/log/lighttpd.error.log
/usr/local/logs/access.log
/usr/local/logs/samba.log
/usr/local/lsws/conf/httpd_conf.xml
/usr/local/lsws/logs/error.log
/usr/local/mysql/data/mysql-bin.index
/usr/local/mysql/data/mysql-bin.log
/usr/local/mysql/data/mysql-slow.log
/usr/local/mysql/data/mysql.err
/usr/local/mysql/data/mysql.log
/usr/local/mysql/data/mysqlderror.log
/usr/local/nginx/conf/nginx.conf
/usr/local/pgsql/bin/pg_passwd
/usr/local/pgsql/data/passwd
/usr/local/pgsql/data/pg_hba.conf
/usr/local/pgsql/data/pg_log
/usr/local/pgsql/data/postgresql.conf
/usr/local/pgsql/data/postgresql.log
/usr/local/php/apache.conf
/usr/local/php/apache.conf.php
/usr/local/php/apache2.conf
/usr/local/php/apache2.conf.php
/usr/local/php/httpd.conf
/usr/local/php/httpd.conf.php
/usr/local/php/lib/php.ini
/usr/local/php4/apache.conf
/usr/local/php4/apache.conf.php
/usr/local/php4/apache2.conf
/usr/local/php4/apache2.conf.php
/usr/local/php4/httpd.conf
/usr/local/php4/httpd.conf.php
/usr/local/php4/lib/php.ini
/usr/local/php5/apache.conf
/usr/local/php5/apache.conf.php
/usr/local/php5/apache2.conf
/usr/local/php5/apache2.conf.php
/usr/local/php5/httpd.conf
/usr/local/php5/httpd.conf.php
/usr/local/php5/lib/php.ini
/usr/local/psa/admin/conf/php.ini
/usr/local/psa/admin/conf/site_isolation_settings.ini
/usr/local/psa/admin/htdocs/domains/databases/phpmyadmin/libraries/config.default.php
/usr/local/psa/admin/logs/httpsd_access_log
/usr/local/psa/admin/logs/panel.log
/usr/local/pureftpd/etc/pure-ftpd.conf
/usr/local/pureftpd/etc/pureftpd.pdb
/usr/local/pureftpd/sbin/pure-config.pl
/usr/local/samba/lib/log.user
/usr/local/samba/lib/smb.conf.user
/usr/local/sb/config
/usr/local/squirrelmail/www/readme
/usr/local/zend/etc/php.ini
/usr/local/zeus/web/global.cfg
/usr/local/zeus/web/log/errors
/usr/pkg/etc/httpd/httpd-default.conf
/usr/pkg/etc/httpd/httpd-vhosts.conf
/usr/pkg/etc/httpd/httpd.conf
/usr/pkgsrc/net/pureftpd/pure-ftpd.conf
/usr/pkgsrc/net/pureftpd/pureftpd.passwd
/usr/pkgsrc/net/pureftpd/pureftpd.pdb
/usr/ports/contrib/pure-ftpd/pure-ftpd.conf
/usr/ports/contrib/pure-ftpd/pureftpd.passwd
/usr/ports/contrib/pure-ftpd/pureftpd.pdb
/usr/ports/ftp/pure-ftpd/pure-ftpd.conf
/usr/ports/ftp/pure-ftpd/pureftpd.passwd
/usr/ports/ftp/pure-ftpd/pureftpd.pdb
/usr/ports/net/pure-ftpd/pure-ftpd.conf
/usr/ports/net/pure-ftpd/pureftpd.passwd
/usr/ports/net/pure-ftpd/pureftpd.pdb
/usr/sbin/mudlogd
/usr/sbin/mudpasswd
/usr/sbin/pure-config.pl
/usr/share/adduser/adduser.conf
/usr/share/logs/catalina.err
/usr/share/logs/catalina.out
/usr/share/squirrelmail/config/config.php
/usr/share/squirrelmail/plugins/squirrel_logger/setup.php
/usr/share/tomcat/logs/catalina.err
/usr/share/tomcat/logs/catalina.out
/usr/share/tomcat6/conf/context.xml
/usr/share/tomcat6/conf/logging.properties
/usr/share/tomcat6/conf/server.xml
/usr/share/tomcat6/conf/workers.properties
/usr/share/tomcat6/logs/catalina.err
/usr/share/tomcat6/logs/catalina.out
/usr/spool/lp/log
/usr/spool/mqueue/syslog
/var/adm/acct/sum/loginlog
/var/adm/aculog
/var/adm/aculogs
/var/adm/crash/unix
/var/adm/crash/vmcore
/var/adm/cron/log
/var/adm/dtmp
/var/adm/lastlog/username
/var/adm/log/asppp.log
/var/adm/log/xferlog
/var/adm/loginlog
/var/adm/lp/lpd-errs
/var/adm/messages
/var/adm/pacct
/var/adm/qacct
/var/adm/ras/bootlog
/var/adm/ras/errlog
/var/adm/sulog
/var/adm/syslog
/var/adm/utmp
/var/adm/utmpx
/var/adm/vold.log
/var/adm/wtmp
/var/adm/wtmpx
/var/adm/x0msgs
/var/apache/conf/httpd.conf
/var/cpanel/cpanel.config
/var/cpanel/tomcat.options
/var/cron/log
/var/data/mysql-bin.index
/var/lib/mysql/my.cnf
/var/lib/pgsql/data/postgresql.conf
/var/lib/squirrelmail/prefs/squirrelmail.log
/var/lighttpd.log
/var/local/www/conf/php.ini
/var/log/access.log
/var/log/access_log
/var/log/apache/access.log
/var/log/apache/access_log
/var/log/apache/error.log
/var/log/apache/error_log
/var/log/apache2/access.log
/var/log/apache2/access_log
/var/log/apache2/error.log
/var/log/apache2/error_log
/var/log/apache2/squirrelmail.err.log
/var/log/apache2/squirrelmail.log
/var/log/auth.log
/var/log/auth.log
/var/log/authlog
/var/log/boot.log
/var/log/cron/var/log/postgres.log
/var/log/daemon.log
/var/log/daemon.log.1
/var/log/data/mysql-bin.index
/var/log/error.log
/var/log/error_log
/var/log/exim/mainlog
/var/log/exim/paniclog
/var/log/exim/rejectlog
/var/log/exim_mainlog
/var/log/exim_paniclog
/var/log/exim_rejectlog
/var/log/ftp-proxy
/var/log/ftp-proxy/ftp-proxy.log
/var/log/ftplog
/var/log/httpd/access.log
/var/log/httpd/access_log
/var/log/httpd/error.log
/var/log/httpd/error_log
/var/log/ipfw
/var/log/ipfw.log
/var/log/ipfw.today
/var/log/ipfw/ipfw.log
/var/log/kern.log
/var/log/kern.log.1
/var/log/lighttpd.access.log
/var/log/lighttpd.error.log
/var/log/lighttpd/access.log
/var/log/lighttpd/access.www.log
/var/log/lighttpd/error.log
/var/log/lighttpd/error.www.log
/var/log/log.smb
/var/log/mail.err
/var/log/mail.info
/var/log/mail.log
/var/log/mail.log
/var/log/mail.warn
/var/log/maillog
/var/log/messages
/var/log/messages.1
/var/log/muddleftpd
/var/log/muddleftpd.conf
/var/log/mysql-bin.index
/var/log/mysql.err
/var/log/mysql.log
/var/log/mysql/data/mysql-bin.index
/var/log/mysql/mysql-bin.index
/var/log/mysql/mysql-bin.log
/var/log/mysql/mysql-slow.log
/var/log/mysql/mysql.log
/var/log/mysqlderror.log
/var/log/news.all
/var/log/news/news.all
/var/log/news/news.crit
/var/log/news/news.err
/var/log/news/news.notice
/var/log/news/suck.err
/var/log/news/suck.notice
/var/log/nginx.access_log
/var/log/nginx.error_log
/var/log/nginx/access.log
/var/log/nginx/access_log
/var/log/nginx/error.log
/var/log/nginx/error_log
/var/log/pgsql/pgsql.log
/var/log/pgsql8.log
/var/log/pgsql_log
/var/log/pm-powersave.log
/var/log/poplog
/var/log/postgres/pg_backup.log
/var/log/postgres/postgres.log
/var/log/postgresql.log
/var/log/postgresql/main.log
/var/log/postgresql/postgres.log
/var/log/postgresql/postgresql-8.1-main.log
/var/log/postgresql/postgresql-8.3-main.log
/var/log/postgresql/postgresql-8.4-main.log
/var/log/postgresql/postgresql-9.0-main.log
/var/log/postgresql/postgresql-9.1-main.log
/var/log/postgresql/postgresql.log
/var/log/proftpd
/var/log/proftpd.access_log
/var/log/proftpd.xferlog
/var/log/proftpd/xferlog.legacy
/var/log/pure-ftpd/pure-ftpd.log
/var/log/pureftpd.log
/var/log/samba.log
/var/log/samba.log1
/var/log/samba.log2
/var/log/samba/log.nmbd
/var/log/samba/log.smbd
/var/log/squirrelmail.log
/var/log/sso/sso.log
/var/log/sw-cp-server/error_log
/var/log/syslog
/var/log/syslog.1
/var/log/thttpd_log
/var/log/tomcat6/catalina.out
/var/log/ufw.log
/var/log/user.log
/var/log/user.log.1
/var/log/vmware/hostd-1.log
/var/log/vmware/hostd.log
/var/log/vsftpd.log
/var/log/webmin/miniserv.log
/var/log/xferlog
/var/log/xorg.0.log
/var/logs/access.log
/var/lp/logs/lpnet
/var/lp/logs/lpsched
/var/lp/logs/requests
/var/mysql-bin.index
/var/mysql.log
/var/nm2/postgresql.conf
/var/postgresql/db/postgresql.conf
/var/postgresql/log/postgresql.log
/var/saf/_log
/var/saf/port/log
/var/www/.lighttpdpassword
/var/www/conf
/var/www/conf/httpd.conf
/var/www/html/squirrelmail-1.2.9/config/config.php
/var/www/html/squirrelmail/config/config.php
/var/www/logs/access.log
/var/www/logs/access_log
/var/www/logs/error.log
/var/www/logs/error_log
/var/www/squirrelmail/config/config.php
/volumes/macintosh_hd1/opt/apache/conf/httpd.conf
/volumes/macintosh_hd1/opt/apache2/conf/httpd.conf
/volumes/macintosh_hd1/opt/httpd/conf/httpd.conf
/volumes/macintosh_hd1/usr/local/php/httpd.conf.php
/volumes/macintosh_hd1/usr/local/php/lib/php.ini
/volumes/macintosh_hd1/usr/local/php4/httpd.conf.php
/volumes/macintosh_hd1/usr/local/php5/httpd.conf.php
/volumes/webbackup/opt/apache2/conf/httpd.conf
/volumes/webbackup/private/etc/httpd/httpd.conf
/volumes/webbackup/private/etc/httpd/httpd.conf.default
/wamp/bin/apache/apache2.2.21/conf/httpd.conf
/wamp/bin/apache/apache2.2.21/logs/access.log
/wamp/bin/apache/apache2.2.21/logs/error.log
/wamp/bin/apache/apache2.2.21/wampserver.conf
/wamp/bin/apache/apache2.2.22/conf/httpd.conf
/wamp/bin/apache/apache2.2.22/conf/wampserver.conf
/wamp/bin/apache/apache2.2.22/logs/access.log
/wamp/bin/apache/apache2.2.22/logs/error.log
/wamp/bin/apache/apache2.2.22/wampserver.conf
/wamp/bin/mysql/mysql5.5.16/data/mysql-bin.index
/wamp/bin/mysql/mysql5.5.16/my.ini
/wamp/bin/mysql/mysql5.5.16/wampserver.conf
/wamp/bin/mysql/mysql5.5.24/data/mysql-bin.index
/wamp/bin/mysql/mysql5.5.24/my.ini
/wamp/bin/mysql/mysql5.5.24/wampserver.conf
/wamp/bin/php/php5.3.8/php.ini
/wamp/bin/php/php5.4.3/php.ini
/wamp/logs/access.log
/wamp/logs/apache_error.log
/wamp/logs/genquery.log
/wamp/logs/mysql.log
/wamp/logs/slowquery.log
/web/conf/php.ini
/windows/comsetup.log
/windows/debug/netsetup.log
/windows/odbc.ini
/windows/php.ini
/windows/repair/setup.log
/windows/setupact.log
/windows/setupapi.log
/windows/setuperr.log
/windows/win.ini
/windows/system32/drivers/etc/hosts
/windows/system32/drivers/etc/lmhosts.sam
/windows/system32/drivers/etc/networks
/windows/system32/drivers/etc/protocol
/windows/system32/drivers/etc/services
/windows/system32/logfiles/firewall/pfirewall.log
/windows/system32/logfiles/firewall/pfirewall.log.old
/windows/system32/logfiles/msftpsvc
/windows/system32/logfiles/msftpsvc1
/windows/system32/logfiles/msftpsvc2
/windows/system32/logfiles/smtpsvc
/windows/system32/logfiles/smtpsvc1
/windows/system32/logfiles/smtpsvc2
/windows/system32/logfiles/smtpsvc3
/windows/system32/logfiles/smtpsvc4
/windows/system32/logfiles/smtpsvc5
/windows/system32/logfiles/w3svc/inetsvn1.log
/windows/system32/logfiles/w3svc1/inetsvn1.log
/windows/system32/logfiles/w3svc2/inetsvn1.log
/windows/system32/logfiles/w3svc3/inetsvn1.log
/windows/system32/macromed/flash/flashinstall.log
/windows/system32/macromed/flash/install.log
/windows/updspapi.log
/windows/windowsupdate.log
/windows/wmsetup.log
/winnt/php.ini
/winnt/system32/logfiles/firewall/pfirewall.log
/winnt/system32/logfiles/firewall/pfirewall.log.old
/winnt/system32/logfiles/msftpsvc
/winnt/system32/logfiles/msftpsvc1
/winnt/system32/logfiles/msftpsvc2
/winnt/system32/logfiles/smtpsvc
/winnt/system32/logfiles/smtpsvc1
/winnt/system32/logfiles/smtpsvc2
/winnt/system32/logfiles/smtpsvc3
/winnt/system32/logfiles/smtpsvc4
/winnt/system32/logfiles/smtpsvc5
/winnt/system32/logfiles/w3svc/inetsvn1.log
/winnt/system32/logfiles/w3svc1/inetsvn1.log
/winnt/system32/logfiles/w3svc2/inetsvn1.log
/winnt/system32/logfiles/w3svc3/inetsvn1.log
/www/apache/conf/httpd.conf
/www/conf/httpd.conf
/www/logs/freebsddiary-access_log
/www/logs/freebsddiary-error.log
/www/logs/proftpd.system.log
/xampp/apache/bin/php.ini
/xampp/apache/conf/httpd.conf
/xampp/apache/logs/access.log
/xampp/apache/logs/error.log
/xampp/filezillaftp/filezilla server.xml
/xampp/htdocs/aca.txt
/xampp/htdocs/admin.php
/xampp/htdocs/leer.txt
/xampp/mercurymail/mercury.ini
/xampp/mysql/data/mysql-bin.index
/xampp/mysql/data/mysql.err
/xampp/php/php.ini
/xampp/phpmyadmin/config.inc.php
/xampp/sendmail/sendmail.ini
/xampp/sendmail/sendmail.log
/xampp/webalizer/webalizer.conf
\autoexec.bat
\boot.ini
\inetpub\wwwroot\web.config
\web.config
\windows\system32\drivers\etc\hosts
\windows\win.ini

# Reference: https://repo.theoremforge.com/pentesting/tools/blob/0f1f0578739870b633c267789120d85982545a69/Uncategorized/Dump/lfiunix.txt

/etc/apache2/.htpasswd
/etc/apache/.htpasswd
/etc/master.passwd
/etc/muddleftpd/muddleftpd.passwd
/etc/muddleftpd/passwd
/etc/passwd
/etc/passwd~
/etc/passwd-
/etc/pureftpd.passwd
/etc/samba/private/smbpasswd
/etc/samba/smbpasswd
/etc/security/opasswd
/etc/security/passwd
/etc/smbpasswd
\Program Files\xampp\apache\conf\httpd.conf
/usr/local/pgsql/bin/pg_passwd
/usr/local/pgsql/data/passwd
/usr/pkgsrc/net/pureftpd/pureftpd.passwd
/usr/ports/contrib/pure-ftpd/pureftpd.passwd
/usr/ports/ftp/pure-ftpd/pureftpd.passwd
/usr/ports/net/pure-ftpd/pureftpd.passwd
/var/log/exim_rejectlog/etc/passwd
/etc/mysql/conf.d/old_passwords.cnf
/etc/password.master
/var/www/.lighttpdpassword
/Volumes/Macintosh_HD1/opt/apache2/conf/httpd.conf
/Volumes/Macintosh_HD1/opt/apache/conf/httpd.conf
/Volumes/Macintosh_HD1/opt/httpd/conf/httpd.conf
/Volumes/Macintosh_HD1/usr/local/php4/httpd.conf.php
/Volumes/Macintosh_HD1/usr/local/php5/httpd.conf.php
/Volumes/Macintosh_HD1/usr/local/php/httpd.conf.php
/Volumes/Macintosh_HD1/usr/local/php/lib/php.ini
/Volumes/webBackup/opt/apache2/conf/httpd.conf
/Volumes/webBackup/private/etc/httpd/httpd.conf
/Volumes/webBackup/private/etc/httpd/httpd.conf.default

# Reference: https://pastebin.com/KgPsDXjg

/etc/passwd
/etc/crontab
/etc/hosts
/etc/my.cnf
/etc/.htpasswd
/root/.bash_history
/etc/named.conf
/proc/self/environ
/etc/php.ini
/bin/php.ini
/etc/httpd/php.ini
/usr/lib/php.ini
/usr/lib/php/php.ini
/usr/local/etc/php.ini
/usr/local/lib/php.ini
/usr/local/php/lib/php.ini
/usr/local/php4/lib/php.ini
/usr/local/php5/lib/php.ini
/usr/local/apache/conf/php.ini
/etc/php4.4/fcgi/php.ini
/etc/php4/apache/php.ini
/etc/php4/apache2/php.ini
/etc/php5/apache/php.ini
/etc/php5/apache2/php.ini
/etc/php/7.4/apache2/php.ini
/etc/php/php.ini
/usr/local/apache/conf/modsec.conf
/var/cpanel/cpanel.config
/proc/self/environ
/proc/self/fd/2
/etc/ssh/sshd_config
/var/lib/mysql/my.cnf
/etc/mysql/my.cnf
/etc/my.cnf
/etc/logrotate.d/proftpd
/www/logs/proftpd.system.log
/var/log/proftpd
/etc/proftp.conf
/etc/protpd/proftpd.conf
/etc/vhcs2/proftpd/proftpd.conf
/etc/proftpd/modules.conf
/etc/vsftpd.chroot_list
/etc/vsftpd/vsftpd.conf
/etc/vsftpd.conf
/etc/chrootUsers
/etc/wu-ftpd/ftpaccess
/etc/wu-ftpd/ftphosts
/etc/wu-ftpd/ftpusers
/usr/sbin/pure-config.pl
/usr/etc/pure-ftpd.conf
/etc/pure-ftpd/pure-ftpd.conf
/usr/local/etc/pure-ftpd.conf
/usr/local/etc/pureftpd.pdb
/usr/local/pureftpd/etc/pureftpd.pdb
/usr/local/pureftpd/sbin/pure-config.pl
/usr/local/pureftpd/etc/pure-ftpd.conf
/etc/pure-ftpd.conf
/etc/pure-ftpd/pure-ftpd.pdb
/etc/pureftpd.pdb
/etc/pureftpd.passwd
/etc/pure-ftpd/pureftpd.pdb
/var/log/ftp-proxy
/etc/logrotate.d/ftp
/etc/ftpchroot
/etc/ftphosts
/etc/smbpasswd
/etc/smb.conf
/etc/samba/smb.conf
/etc/samba/samba.conf
/etc/samba/smb.conf.user
/etc/samba/smbpasswd
/etc/samba/smbusers
/var/lib/pgsql/data/postgresql.conf
/var/postgresql/db/postgresql.conf
/etc/ipfw.conf
/etc/firewall.rules
/etc/ipfw.rules
/usr/local/etc/webmin/miniserv.conf
/etc/webmin/miniserv.conf
/usr/local/etc/webmin/miniserv.users
/etc/webmin/miniserv.users
/etc/squirrelmail/config/config.php
/etc/squirrelmail/config.php
/etc/httpd/conf.d/squirrelmail.conf
/usr/share/squirrelmail/config/config.php
/private/etc/squirrelmail/config/config.php
/srv/www/htdos/squirrelmail/config/config.php

# Web shells

/var/www/html/backdoor.php
/var/www/html/b374k.php
/var/www/html/c99.php
/var/www/html/cmd.php
/var/www/html/r57.php
/var/www/html/shell.php
/var/www/html/wso.php

# Misc

/app/app.js
/app/configure.js
/app/config/config.json
/etc/grafana/grafana.ini
/opt/kibana/config/kibana.yml
/etc/kibana/kibana.yml
/etc/elasticsearch/elasticsearch.yml
