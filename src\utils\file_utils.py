# src/utils/file_utils.py - Utilitários para manipulação de arquivos

import json
import csv
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

def save_json(data: Dict[str, Any], file_path: Path, indent: int = 2) -> bool:
    """
    Salva dados em formato JSON
    
    Args:
        data: Dados para salvar
        file_path: Caminho do arquivo
        indent: Indentação do JSON
        
    Returns:
        bool: True se salvou com sucesso
    """
    try:
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False, default=str)
        
        return True
    except Exception as e:
        print(f"Erro ao salvar JSON {file_path}: {e}")
        return False

def load_json(file_path: Path) -> Optional[Dict[str, Any]]:
    """
    Carrega dados de arquivo JSON
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        Dict com dados ou None se erro
    """
    try:
        if not file_path.exists():
            return None
            
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Erro ao carregar JSON {file_path}: {e}")
        return None

def save_csv(data: List[Dict[str, Any]], file_path: Path, fieldnames: Optional[List[str]] = None) -> bool:
    """
    Salva dados em formato CSV
    
    Args:
        data: Lista de dicionários para salvar
        file_path: Caminho do arquivo
        fieldnames: Nomes das colunas (opcional)
        
    Returns:
        bool: True se salvou com sucesso
    """
    try:
        if not data:
            return False
            
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        if not fieldnames:
            fieldnames = list(data[0].keys())
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        return True
    except Exception as e:
        print(f"Erro ao salvar CSV {file_path}: {e}")
        return False

def save_text(content: str, file_path: Path) -> bool:
    """
    Salva conteúdo em arquivo de texto
    
    Args:
        content: Conteúdo para salvar
        file_path: Caminho do arquivo
        
    Returns:
        bool: True se salvou com sucesso
    """
    try:
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
    except Exception as e:
        print(f"Erro ao salvar texto {file_path}: {e}")
        return False

def load_text(file_path: Path) -> Optional[str]:
    """
    Carrega conteúdo de arquivo de texto
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        str: Conteúdo do arquivo ou None se erro
    """
    try:
        if not file_path.exists():
            return None
            
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Erro ao carregar texto {file_path}: {e}")
        return None

def load_lines(file_path: Path) -> List[str]:
    """
    Carrega linhas de arquivo de texto
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        List[str]: Lista de linhas
    """
    try:
        if not file_path.exists():
            return []
            
        with open(file_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines() if line.strip()]
    except Exception as e:
        print(f"Erro ao carregar linhas {file_path}: {e}")
        return []

def create_directory_structure(base_path: Path, structure: Dict[str, Any]) -> bool:
    """
    Cria estrutura de diretórios
    
    Args:
        base_path: Diretório base
        structure: Estrutura de diretórios
        
    Returns:
        bool: True se criou com sucesso
    """
    try:
        for name, content in structure.items():
            current_path = base_path / name
            
            if isinstance(content, dict):
                current_path.mkdir(exist_ok=True, parents=True)
                create_directory_structure(current_path, content)
            else:
                current_path.mkdir(exist_ok=True, parents=True)
        
        return True
    except Exception as e:
        print(f"Erro ao criar estrutura de diretórios: {e}")
        return False

def get_file_size(file_path: Path) -> int:
    """
    Obtém tamanho do arquivo em bytes
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        int: Tamanho em bytes
    """
    try:
        return file_path.stat().st_size if file_path.exists() else 0
    except Exception:
        return 0

def get_file_age(file_path: Path) -> Optional[datetime]:
    """
    Obtém data de modificação do arquivo
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        datetime: Data de modificação ou None
    """
    try:
        if file_path.exists():
            timestamp = file_path.stat().st_mtime
            return datetime.fromtimestamp(timestamp)
        return None
    except Exception:
        return None

def backup_file(file_path: Path, backup_suffix: str = ".bak") -> bool:
    """
    Cria backup de arquivo
    
    Args:
        file_path: Arquivo para backup
        backup_suffix: Sufixo do backup
        
    Returns:
        bool: True se criou backup
    """
    try:
        if not file_path.exists():
            return False
            
        backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
        
        import shutil
        shutil.copy2(file_path, backup_path)
        
        return True
    except Exception as e:
        print(f"Erro ao criar backup de {file_path}: {e}")
        return False

def clean_old_files(directory: Path, max_age_days: int = 30) -> int:
    """
    Remove arquivos antigos de um diretório
    
    Args:
        directory: Diretório para limpar
        max_age_days: Idade máxima em dias
        
    Returns:
        int: Número de arquivos removidos
    """
    try:
        if not directory.exists():
            return 0
            
        removed_count = 0
        cutoff_date = datetime.now().timestamp() - (max_age_days * 24 * 60 * 60)
        
        for file_path in directory.rglob("*"):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_date:
                file_path.unlink()
                removed_count += 1
        
        return removed_count
    except Exception as e:
        print(f"Erro ao limpar arquivos antigos: {e}")
        return 0

def merge_json_files(file_paths: List[Path], output_path: Path) -> bool:
    """
    Mescla múltiplos arquivos JSON em um só
    
    Args:
        file_paths: Lista de arquivos JSON
        output_path: Arquivo de saída
        
    Returns:
        bool: True se mesclou com sucesso
    """
    try:
        merged_data = {}
        
        for file_path in file_paths:
            data = load_json(file_path)
            if data:
                merged_data[file_path.stem] = data
        
        return save_json(merged_data, output_path)
    except Exception as e:
        print(f"Erro ao mesclar arquivos JSON: {e}")
        return False
