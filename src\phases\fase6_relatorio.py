# src/phases/fase6_relatorio.py - Fase 6: Relatório Inteligente e Plano de Ação

import os
import json
import base64
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple
from dataclasses import dataclass, asdict

# Imports da nova estrutura
from ..core.logger import get_logger
from ..core.config import DEFAULT_RELATORIO_CONFIG
from ..utils.file_utils import save_json, load_json, save_csv

@dataclass
class RelatorioConfig:
    """Configurações para geração de relatório"""
    incluir_graficos: bool = True
    formato_saida: List[str] = None
    nivel_detalhamento: str = "completo"  # basico, completo, executivo
    incluir_recomendacoes: bool = True
    
    def __post_init__(self):
        if self.formato_saida is None:
            self.formato_saida = ["json", "html", "txt"]

@dataclass
class VulnerabilidadeConsolidada:
    """Vulnerabilidade consolidada de todas as fases"""
    id: str
    tipo: str
    severidade: str
    url_alvo: str
    descricao: str
    impacto_tecnico: str
    impacto_negocio: str
    prova_conceito: str
    recomendacao: str
    cvss_score: Optional[float] = None
    fase_descoberta: str = "unknown"
    status_exploracao: str = "nao_explorada"

@dataclass
class ResumoExecutivo:
    """Resumo executivo do pentest"""
    total_vulnerabilidades: int
    vulnerabilidades_criticas: int
    vulnerabilidades_altas: int
    vulnerabilidades_medias: int
    vulnerabilidades_baixas: int
    score_seguranca: float  # 0-100
    nivel_risco_geral: str
    tempo_total_analise: str
    recomendacoes_prioritarias: List[str]

class GeradorRelatorioInteligente:
    """Classe principal para geração de relatório inteligente"""
    
    def __init__(self, alvo: str, diretorio_saida: str, config: RelatorioConfig = None):
        self.alvo = alvo
        self.diretorio_saida = Path(diretorio_saida)
        self.config = config or RelatorioConfig()
        self.dados_consolidados = {}
        self.vulnerabilidades_consolidadas = []
        self._criar_estrutura_diretorios()
        self.logger = self._configurar_logging()
        self._carregar_dados_todas_fases()
    
    def _configurar_logging(self):
        """Configura logging detalhado"""
        self.logger = get_logger(f"relatorio_{self.alvo}")
        return self.logger

    
    def _criar_estrutura_diretorios(self):
        """Cria estrutura de diretórios para a Fase 6"""
        subdirs = [
            "relatorio_final", "graficos", "executivo", "tecnico", "json_output"
        ]
        for subdir in subdirs:
            (self.diretorio_saida / subdir).mkdir(exist_ok=True, parents=True)
    
    def _carregar_dados_todas_fases(self):
        """Carrega dados de todas as fases anteriores"""
        self.logger.info("📊 Carregando dados de todas as fases...")
        
        # Fase 1 - Reconhecimento
        self._carregar_dados_fase1()
        
        # Fase 2 - Enumeração
        self._carregar_dados_fase2()
        
        # Fase 3 - Vulnerabilidades
        self._carregar_dados_fase3()
        
        # Fase 4 - Exploração
        self._carregar_dados_fase4()
        
        # Fase 5 - Pós-exploração
        self._carregar_dados_fase5()
        
        self.logger.info("✅ Dados de todas as fases carregados")

    def _carregar_dados_fase1(self):
        """Carrega dados da Fase 1"""
        try:
            arquivo_httpx = self.diretorio_saida / "subdominios" / "ativos_detalhados.json"
            if arquivo_httpx.exists():
                subdominios_ativos = []
                with open(arquivo_httpx, 'r') as f:
                    for linha in f:
                        if linha.strip():
                            subdominios_ativos.append(json.loads(linha))
                
                self.dados_consolidados['fase1'] = {
                    'subdominios_encontrados': len(subdominios_ativos),
                    'subdominios_ativos': len([s for s in subdominios_ativos if s.get('status_code', 0) == 200]),
                    'tecnologias_identificadas': self._extrair_tecnologias_fase1(subdominios_ativos)
                }
                self.logger.info(f"Fase 1: {len(subdominios_ativos)} subdomínios carregados")
        except Exception as e:
            self.logger.warning(f"Erro ao carregar Fase 1: {e}")
            self.dados_consolidados['fase1'] = {}

    def _extrair_tecnologias_fase1(self, subdominios: List[Dict]) -> Dict:
        """Extrai estatísticas de tecnologias da Fase 1"""
        tecnologias = {}
        servidores = {}
        
        for sub in subdominios:
            for tech in sub.get('tech', []):
                tecnologias[tech] = tecnologias.get(tech, 0) + 1
            
            servidor = sub.get('server', '')
            if servidor:
                servidores[servidor] = servidores.get(servidor, 0) + 1
        
        return {
            'tecnologias_mais_comuns': sorted(tecnologias.items(), key=lambda x: x[1], reverse=True)[:5],
            'servidores_mais_comuns': sorted(servidores.items(), key=lambda x: x[1], reverse=True)[:3]
        }

    def _carregar_dados_fase2(self):
        """Carrega dados da Fase 2"""
        try:
            relatorios_dir = self.diretorio_saida / "relatorios_fase2"
            if relatorios_dir.exists():
                arquivos = list(relatorios_dir.glob("*.json"))
                if arquivos:
                    arquivo_mais_recente = max(arquivos, key=os.path.getctime)
                    with open(arquivo_mais_recente, 'r') as f:
                        data = json.load(f)
                        self.dados_consolidados['fase2'] = data
                        self.logger.info("Fase 2: Dados de enumeração carregados")
        except Exception as e:
            self.logger.warning(f"Erro ao carregar Fase 2: {e}")
            self.dados_consolidados['fase2'] = {}

    def _carregar_dados_fase3(self):
        """Carrega dados da Fase 3"""
        try:
            relatorios_dir = self.diretorio_saida / "relatorios_fase3"
            if relatorios_dir.exists():
                arquivos = list(relatorios_dir.glob("vulnerabilidades_*.json"))
                if arquivos:
                    arquivo_mais_recente = max(arquivos, key=os.path.getctime)
                    with open(arquivo_mais_recente, 'r') as f:
                        data = json.load(f)
                        self.dados_consolidados['fase3'] = data
                        self._processar_vulnerabilidades_fase3(data.get('vulnerabilidades', []))
                        self.logger.info(f"Fase 3: {len(data.get('vulnerabilidades', []))} vulnerabilidades carregadas")
        except Exception as e:
            self.logger.warning(f"Erro ao carregar Fase 3: {e}")
            self.dados_consolidados['fase3'] = {}

    def _processar_vulnerabilidades_fase3(self, vulnerabilidades: List[Dict]):
        """Processa vulnerabilidades da Fase 3"""
        for vuln in vulnerabilidades:
            vuln_consolidada = VulnerabilidadeConsolidada(
                id=f"vuln_{len(self.vulnerabilidades_consolidadas) + 1}",
                tipo=vuln.get('vulnerability_type', 'Unknown'),
                severidade=vuln.get('severity', 'Medium'),
                url_alvo=vuln.get('target_url', ''),
                descricao=vuln.get('description', ''),
                impacto_tecnico=self._determinar_impacto_tecnico(vuln),
                impacto_negocio=self._determinar_impacto_negocio(vuln),
                prova_conceito=vuln.get('proof_of_concept', ''),
                recomendacao=self._gerar_recomendacao(vuln),
                cvss_score=vuln.get('cvss_score'),
                fase_descoberta="Fase 3 - Análise de Vulnerabilidades",
                status_exploracao="detectada"
            )
            self.vulnerabilidades_consolidadas.append(vuln_consolidada)

    def _carregar_dados_fase4(self):
        """Carrega dados da Fase 4"""
        try:
            relatorios_dir = self.diretorio_saida / "relatorios_fase4"
            if relatorios_dir.exists():
                arquivos = list(relatorios_dir.glob("exploracao_*.json"))
                if arquivos:
                    arquivo_mais_recente = max(arquivos, key=os.path.getctime)
                    with open(arquivo_mais_recente, 'r') as f:
                        data = json.load(f)
                        self.dados_consolidados['fase4'] = data
                        self._atualizar_status_exploracao(data.get('exploits', []))
                        self.logger.info(f"Fase 4: {len(data.get('exploits', []))} explorações carregadas")
        except Exception as e:
            self.logger.warning(f"Erro ao carregar Fase 4: {e}")
            self.dados_consolidados['fase4'] = {}

    def _atualizar_status_exploracao(self, exploits: List[Dict]):
        """Atualiza status de exploração das vulnerabilidades"""
        for exploit in exploits:
            url_alvo = exploit.get('target_url', '')
            for vuln in self.vulnerabilidades_consolidadas:
                if vuln.url_alvo == url_alvo:
                    vuln.status_exploracao = "explorada" if exploit.get('success') else "tentativa_falhada"
                    if exploit.get('business_impact'):
                        vuln.impacto_negocio = exploit.get('business_impact')

    def _carregar_dados_fase5(self):
        """Carrega dados da Fase 5"""
        try:
            relatorios_dir = self.diretorio_saida / "relatorios_fase5"
            if relatorios_dir.exists():
                arquivos = list(relatorios_dir.glob("pos_exploracao_*.json"))
                if arquivos:
                    arquivo_mais_recente = max(arquivos, key=os.path.getctime)
                    with open(arquivo_mais_recente, 'r') as f:
                        data = json.load(f)
                        self.dados_consolidados['fase5'] = data
                        self.logger.info("Fase 5: Dados de pós-exploração carregados")
        except Exception as e:
            self.logger.warning(f"Erro ao carregar Fase 5: {e}")
            self.dados_consolidados['fase5'] = {}

    def _determinar_impacto_tecnico(self, vuln: Dict) -> str:
        """Determina impacto técnico da vulnerabilidade"""
        tipo = vuln.get('vulnerability_type', '').lower()
        
        if 'sql injection' in tipo:
            return "Acesso não autorizado ao banco de dados, possível extração/modificação de dados"
        elif 'xss' in tipo:
            return "Execução de código JavaScript malicioso no navegador do usuário"
        elif 'component' in tipo or 'cve' in tipo:
            return "Exploração de vulnerabilidade conhecida em componente desatualizado"
        else:
            return "Impacto técnico específico da vulnerabilidade identificada"

    def _determinar_impacto_negocio(self, vuln: Dict) -> str:
        """Determina impacto de negócio da vulnerabilidade"""
        severidade = vuln.get('severity', '').lower()
        
        if severidade in ['critical', 'high']:
            return "Alto risco de comprometimento de dados, reputação e conformidade regulatória"
        elif severidade == 'medium':
            return "Risco moderado de exposição de informações e comprometimento da segurança"
        else:
            return "Risco baixo, mas requer atenção para manter postura de segurança"

    def _gerar_recomendacao(self, vuln: Dict) -> str:
        """Gera recomendação específica para a vulnerabilidade"""
        tipo = vuln.get('vulnerability_type', '').lower()
        
        if 'sql injection' in tipo:
            return "Implementar prepared statements, validação de entrada e princípio do menor privilégio"
        elif 'xss' in tipo:
            return "Implementar CSP, sanitização de saídas e validação rigorosa de entradas"
        elif 'component' in tipo:
            return "Atualizar componente para versão mais recente e aplicar patches de segurança"
        else:
            return "Revisar configurações de segurança e aplicar melhores práticas"

    def gerar_resumo_executivo(self) -> ResumoExecutivo:
        """Gera resumo executivo do pentest"""
        total_vulns = len(self.vulnerabilidades_consolidadas)
        criticas = len([v for v in self.vulnerabilidades_consolidadas if v.severidade.lower() == 'critical'])
        altas = len([v for v in self.vulnerabilidades_consolidadas if v.severidade.lower() == 'high'])
        medias = len([v for v in self.vulnerabilidades_consolidadas if v.severidade.lower() == 'medium'])
        baixas = len([v for v in self.vulnerabilidades_consolidadas if v.severidade.lower() in ['low', 'info']])
        
        # Calcula score de segurança (0-100, onde 100 é mais seguro)
        score_seguranca = max(0, 100 - (criticas * 25 + altas * 15 + medias * 8 + baixas * 2))
        
        # Determina nível de risco geral
        if criticas > 0:
            nivel_risco = "CRÍTICO"
        elif altas > 2:
            nivel_risco = "ALTO"
        elif altas > 0 or medias > 5:
            nivel_risco = "MÉDIO"
        else:
            nivel_risco = "BAIXO"
        
        return ResumoExecutivo(
            total_vulnerabilidades=total_vulns,
            vulnerabilidades_criticas=criticas,
            vulnerabilidades_altas=altas,
            vulnerabilidades_medias=medias,
            vulnerabilidades_baixas=baixas,
            score_seguranca=score_seguranca,
            nivel_risco_geral=nivel_risco,
            tempo_total_analise="Estimado baseado nas fases executadas",
            recomendacoes_prioritarias=self._gerar_recomendacoes_prioritarias(criticas, altas)
        )

    def _gerar_recomendacoes_prioritarias(self, criticas: int, altas: int) -> List[str]:
        """Gera recomendações prioritárias baseadas nas vulnerabilidades"""
        recomendacoes = []
        
        if criticas > 0:
            recomendacoes.append("URGENTE: Correção imediata de vulnerabilidades críticas")
        
        if altas > 0:
            recomendacoes.append("Correção prioritária de vulnerabilidades de alta severidade")
        
        recomendacoes.extend([
            "Implementação de programa de gestão de vulnerabilidades",
            "Treinamento da equipe em práticas seguras de desenvolvimento",
            "Estabelecimento de processo de resposta a incidentes",
            "Monitoramento contínuo de segurança"
        ])
        
        return recomendacoes[:5]  # Limita a 5 recomendações

    def gerar_relatorio_json(self) -> str:
        """Gera relatório completo em JSON"""
        resumo = self.gerar_resumo_executivo()
        
        relatorio_completo = {
            "metadata": {
                "alvo": self.alvo,
                "timestamp": datetime.now().isoformat(),
                "versao_guardian_ia": "1.0",
                "fases_executadas": list(self.dados_consolidados.keys())
            },
            "resumo_executivo": asdict(resumo),
            "vulnerabilidades": [asdict(v) for v in self.vulnerabilidades_consolidadas],
            "dados_por_fase": self.dados_consolidados,
            "estatisticas": {
                "subdominios_analisados": self.dados_consolidados.get('fase1', {}).get('subdominios_encontrados', 0),
                "urls_testadas": len(set([v.url_alvo for v in self.vulnerabilidades_consolidadas])),
                "ferramentas_utilizadas": ["subfinder", "httpx", "nuclei", "sqlmap", "dalfox"],
                "tempo_total_estimado": "Baseado na execução das fases"
            }
        }
        
        arquivo_json = self.diretorio_saida / "json_output" / f"relatorio_completo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(relatorio_completo, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📄 Relatório JSON gerado: {arquivo_json}")
        return str(arquivo_json)

    def gerar_relatorio_html(self) -> str:
        """Gera relatório em HTML"""
        resumo = self.gerar_resumo_executivo()
        
        html_template = """
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guardian IA - Relatório de Segurança</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 2.5em; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
        .subtitle { color: #7f8c8d; font-style: italic; }
        .summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .risk-level { font-size: 1.5em; font-weight: bold; text-align: center; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .risk-critico { background-color: #e74c3c; }
        .risk-alto { background-color: #e67e22; }
        .risk-medio { background-color: #f39c12; }
        .risk-baixo { background-color: #27ae60; }
        .vuln-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; background: #fafafa; }
        .vuln-header { font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
        .severity-critical { border-left: 5px solid #e74c3c; }
        .severity-high { border-left: 5px solid #e67e22; }
        .severity-medium { border-left: 5px solid #f39c12; }
        .severity-low { border-left: 5px solid #27ae60; }
        .recommendations { background: #ecf0f1; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ GUARDIAN IA</div>
            <div class="subtitle">"Protegendo o invisível. Antecipando o inevitável."</div>
            <h2>Relatório de Análise de Segurança</h2>
            <p><strong>Alvo:</strong> {{ alvo }} | <strong>Data:</strong> {{ data }}</p>
        </div>
        
        <div class="summary">
            <h3>📊 Resumo Executivo</h3>
            <div class="risk-level risk-{{ nivel_risco.lower() }}">
                Nível de Risco: {{ nivel_risco }}
            </div>
            <p><strong>Score de Segurança:</strong> {{ score_seguranca }}/100</p>
            <p><strong>Total de Vulnerabilidades:</strong> {{ total_vulnerabilidades }}</p>
            <ul>
                <li>🔴 Críticas: {{ criticas }}</li>
                <li>🟠 Altas: {{ altas }}</li>
                <li>🟡 Médias: {{ medias }}</li>
                <li>🟢 Baixas: {{ baixas }}</li>
            </ul>
        </div>
        
        <h3>🎯 Vulnerabilidades Identificadas</h3>
        {% for vuln in vulnerabilidades %}
        <div class="vuln-card severity-{{ vuln.severidade.lower() }}">
            <div class="vuln-header">{{ vuln.tipo }} - {{ vuln.severidade }}</div>
            <p><strong>URL:</strong> {{ vuln.url_alvo }}</p>
            <p><strong>Descrição:</strong> {{ vuln.descricao }}</p>
            <p><strong>Impacto no Negócio:</strong> {{ vuln.impacto_negocio }}</p>
            <p><strong>Recomendação:</strong> {{ vuln.recomendacao }}</p>
        </div>
        {% endfor %}
        
        <div class="recommendations">
            <h3>🚀 Recomendações Prioritárias</h3>
            <ol>
                {% for rec in recomendacoes %}
                <li>{{ rec }}</li>
                {% endfor %}
            </ol>
        </div>
        
        <div class="footer">
            <p>Relatório gerado pelo Guardian IA - Sistema de Análise de Segurança Automatizada</p>
            <p>Para mais informações sobre as vulnerabilidades, consulte o relatório técnico detalhado.</p>
        </div>
    </div>
</body>
</html>
        """
        
        try:
            from jinja2 import Template
            template = Template(html_template)
            
            html_content = template.render(
                alvo=self.alvo,
                data=datetime.now().strftime("%d/%m/%Y %H:%M"),
                nivel_risco=resumo.nivel_risco_geral,
                score_seguranca=resumo.score_seguranca,
                total_vulnerabilidades=resumo.total_vulnerabilidades,
                criticas=resumo.vulnerabilidades_criticas,
                altas=resumo.vulnerabilidades_altas,
                medias=resumo.vulnerabilidades_medias,
                baixas=resumo.vulnerabilidades_baixas,
                vulnerabilidades=self.vulnerabilidades_consolidadas,
                recomendacoes=resumo.recomendacoes_prioritarias
            )
        except ImportError:
            # Fallback simples sem Jinja2
            html_content = f"""
<!DOCTYPE html>
<html>
<head><title>Guardian IA - Relatório</title></head>
<body>
<h1>Guardian IA - Relatório de Segurança</h1>
<h2>Alvo: {self.alvo}</h2>
<p>Total de Vulnerabilidades: {resumo.total_vulnerabilidades}</p>
<p>Nível de Risco: {resumo.nivel_risco_geral}</p>
<p>Score de Segurança: {resumo.score_seguranca}/100</p>
</body>
</html>
            """
        
        arquivo_html = self.diretorio_saida / "relatorio_final" / f"relatorio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(arquivo_html, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"📄 Relatório HTML gerado: {arquivo_html}")
        return str(arquivo_html)

    def executar_fase_completa(self) -> Dict:
        """Executa o fluxo completo da Fase 6"""
        inicio = datetime.now()
        try:
            self.logger.info(f"🚀 INICIANDO FASE 6: RELATÓRIO INTELIGENTE PARA {self.alvo}")
            
            # Gera relatórios em diferentes formatos
            arquivos_gerados = []
            
            if "json" in self.config.formato_saida:
                arquivo_json = self.gerar_relatorio_json()
                arquivos_gerados.append(arquivo_json)
            
            if "html" in self.config.formato_saida:
                arquivo_html = self.gerar_relatorio_html()
                arquivos_gerados.append(arquivo_html)
            
            resumo = self.gerar_resumo_executivo()
            
            duracao = datetime.now() - inicio
            self.logger.info("="*60)
            self.logger.info("🎉 FASE 6 CONCLUÍDA COM SUCESSO!")
            self.logger.info(f"📊 Vulnerabilidades consolidadas: {len(self.vulnerabilidades_consolidadas)}")
            self.logger.info(f"📄 Relatórios gerados: {len(arquivos_gerados)}")
            self.logger.info(f"🎯 Score de Segurança: {resumo.score_seguranca}/100")
            self.logger.info(f"⏱️  Duração total: {duracao}")
            
            return {
                'sucesso': True,
                'vulnerabilidades_consolidadas': len(self.vulnerabilidades_consolidadas),
                'score_seguranca': resumo.score_seguranca,
                'nivel_risco': resumo.nivel_risco_geral,
                'arquivos_gerados': arquivos_gerados,
                'duracao': str(duracao)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erro crítico na Fase 6: {str(e)}", exc_info=True)
            return {'sucesso': False, 'erro': str(e)}

def run(alvo: str, diretorio_saida: str, config_dict: Dict = None):
    """Interface principal para executar a Fase 6"""
    config = RelatorioConfig(**config_dict) if config_dict else RelatorioConfig()
    gerador = GeradorRelatorioInteligente(alvo, diretorio_saida, config)
    return gerador.executar_fase_completa()
