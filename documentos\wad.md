# WAD - Web Application Document 

## Nome Projeto:
- **Guardian IA**
"Protegendo o invisível. Antecipando o inevitável."


## Nome do autor:
- <a href="https://www.linkedin.com/in/jo%C3%A3ocardosodias/">João Cardoso Dias</a> 
## Sumário

[1. Introdução](#c1)

[2. Visão Geral da Aplicação Web](#c2)

[3. Projeto Técnico da Aplicação Web](#c3)

[4. Desenvolvimento da Aplicação Web](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](c#8)

[Anexos](#c9)

<br>


# <a name="c1"></a>1. Introdução 

&emsp;A Guardian IA é uma inteligência artificial voltada para a simulação automatizada de ataques cibernéticos em sites web, com o objetivo exclusivo de auxiliar proprietários de sites a identificarem vulnerabilidades de segurança e tomarem medidas preventivas antes que sejam exploradas por agentes maliciosos.

&emsp;Diferente de ferramentas tradicionais de pentest que exigem conhecimento técnico aprofundado, a Guardian IA combina ferramentas de análise ofensiva automatizada (como sqlmap, nmap, entre outras) com o poder interpretativo de modelos de linguagem natural, como o GPT-4, para transformar relatórios técnicos em análises compreensíveis, estruturadas e acionáveis.

&emsp;O sistema é projetado para funcionar em um ambiente controlado e seguro, onde o usuário insere a URL do seu próprio site, e a IA executa testes simulados de forma ética, transparente e com base em boas práticas de cibersegurança. Ao final do processo, é gerado um relatório que detalha as vulnerabilidades encontradas, explica seus riscos e fornece recomendações práticas de correção.

&emsp;A proposta da Guardian IA é democratizar o acesso a testes de segurança, promovendo uma internet mais segura para todos, especialmente para pequenos negócios e desenvolvedores independentes que muitas vezes não têm acesso a consultorias especializadas em segurança digital.



# <a name="c2"></a>2. Visão Geral da Aplicação Web (sprint 1)

## 2.1. Escopo do Projeto 

### 2.1.1. Solução (sprints 1 a 5)

**1. Problema a ser resolvido:**
Muitos desenvolvedores e donos de sites não possuem conhecimento técnico ou ferramentas adequadas para identificar vulnerabilidades de segurança, tornando suas aplicações suscetíveis a ataques como SQL Injection, XSS e CSRF. A falta de testes preventivos acessíveis é uma brecha crítica na segurança digital.

**2. Dados disponíveis:**
A Guardian IA utiliza dados gerados localmente pelas ferramentas de pentest, como logs do sqlmap, nmap e Wapiti. Essas ferramentas fornecem informações técnicas sobre vulnerabilidades, portas abertas, headers inseguros e outros riscos. Nenhum dado sensível é coletado de fontes externas. Fonte: varreduras locais controladas.

**3. Solução proposta:**
Desenvolver uma plataforma que simula ataques de forma ética e automatizada em ambientes seguros, interpretando os resultados com uma IA para gerar relatórios explicativos e personalizados. A solução combina ferramentas de segurança conhecidas com a API da OpenAI, facilitando a compreensão dos riscos e sugerindo correções.

**4. Forma de utilização da solução:**
O usuário acessa a plataforma, insere a URL do seu próprio site e realiza uma verificação de propriedade. A IA clona o site em um ambiente isolado, executa testes de vulnerabilidade, interpreta os resultados e fornece um relatório com explicações técnicas e sugestões de melhoria.

**5. Benefícios esperados:**
Promover a segurança preventiva de aplicações web, reduzir riscos de ataques e democratizar o acesso a testes de segurança. A solução deve capacitar usuários não técnicos a entender e corrigir falhas com facilidade, aumentando a proteção digital de pequenos negócios e desenvolvedores autônomos.

**6. Critério de sucesso e como será avaliado:**
A solução será considerada bem-sucedida se identificar corretamente vulnerabilidades em sites de teste e gerar relatórios claros, com mais de 80% de compreensão por usuários não técnicos. A avaliação será feita com feedbacks diretos, testes controlados e métricas de uso da ferramenta.



### 2.1.4. Value Proposition Canvas (sprint 1):
O Canvas de Proposta de Valor é uma ferramenta essencial para alinhar os produtos e serviços de uma empresa às reais necessidades, dores e desejos dos seus clientes. Ele permite compreender com clareza quais são as principais tarefas que os clientes precisam realizar, quais os problemas que enfrentam (dores) e quais ganhos esperam alcançar. A partir dessa análise, é possível desenhar ofertas mais assertivas, aumentando a geração de valor e reduzindo riscos de insucesso no mercado (Osterwalder et al., 2014).

De acordo com Osterwalder, Pigneur, Bernarda e Smith (2014), o Canvas de Proposta de Valor ajuda a tornar tangível o ajuste entre produto e mercado (product-market fit), fundamental para o desenvolvimento de soluções centradas no cliente. Ao identificar de forma sistemática os "Criadores de Ganhos", "Aliviadores de Dores" e os Produtos e Serviços, a organização consegue estruturar sua proposta de forma estratégica, maximizando impacto e diferenciação no mercado.

Além disso, utilizar essa metodologia contribui para a tomada de decisão orientada, permitindo validar hipóteses de valor de maneira mais ágil e eficaz, especialmente em projetos inovadores, como soluções baseadas em inteligência artificial e cibersegurança.


<div align="center" style="margin-bottom: 1em;">
<p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura X</strong> - Canva Prosposta de Valor</p>
<img src="../assets/canvapropostadevalor.png"><br />
<p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
    Fonte:Produção Autoral

</p>
</div>

---

#### Segmento de Clientes:
O Guardian IA é voltado para donos de sites, desenvolvedores independentes, freelancers, pequenas e médias empresas, criadores de plataformas digitais e profissionais autônomos que precisam garantir a segurança dos seus sites, mas que não possuem conhecimento técnico avançado em cibersegurança. Este público frequentemente encontra barreiras como custos elevados de serviços especializados e a complexidade das ferramentas tradicionais de pentest. A ferramenta também atende empresas que desejam cumprir normas de proteção de dados, como a LGPD e a GDPR, além de proteger sua reputação e seus dados contra ameaças cibernéticas.

---

#### Tarefas dos Clientes:
Os clientes precisam proteger seus sites contra ataques e exploração de vulnerabilidades. Suas principais tarefas envolvem identificar possíveis falhas de segurança, compreender os riscos existentes, receber instruções claras sobre como mitigar esses riscos e manter seus ambientes digitais seguros. Além disso, eles buscam garantir que seus sistemas estejam em conformidade com legislações de privacidade, evitar incidentes de segurança e assegurar uma experiência segura para seus usuários.

---

#### Dores:
As dores dos clientes incluem a dificuldade em acessar serviços de segurança acessíveis e fáceis de usar, já que a maioria das soluções no mercado exige conhecimento técnico avançado. Também enfrentam altos custos com consultorias ou especialistas, além de viverem sob o risco constante de ataques como vazamento de dados, defacement, ataques DDoS, SQL Injection e outros. A dificuldade em interpretar relatórios técnicos e a falta de recursos para implementar medidas de proteção agravam ainda mais sua situação. Por fim, existe o receio de sofrer sanções legais decorrentes da não conformidade com normas de segurança digital.

---

#### Ganhos Esperados: 
Os clientes esperam obter uma solução que proporcione segurança, tranquilidade e autonomia na gestão da segurança dos seus sites. Eles buscam uma ferramenta acessível, de fácil utilização, que permita identificar vulnerabilidades e, principalmente, ofereça orientações claras sobre como corrigi-las. Também desejam economizar recursos financeiros ao evitar consultorias caras e cumprir exigências legais, fortalecendo sua reputação perante clientes e parceiros.

---

#### Produtos e Serviços: 
O Guardian IA oferece uma plataforma online com uma interface intuitiva, onde o usuário insere a URL do seu site e, de forma automática, a IA executa uma série de testes de segurança simulando diversos tipos de ataques. Após a análise, o sistema gera um relatório claro e detalhado, apontando as vulnerabilidades encontradas e indicando medidas de mitigação. A solução inclui um sistema de autenticação para garantir que apenas os proprietários dos sites realizem os testes e opera dentro de um ambiente seguro, onde uma cópia do site é utilizada para os testes, sem afetar o ambiente de produção.

---

#### Aliviadores de Dores:
O Guardian IA elimina a necessidade de conhecimentos técnicos avançados ao automatizar todo o processo de testes de segurança. Sua interface simplificada permite que qualquer pessoa utilize a ferramenta de maneira segura e eficiente. Ao centralizar testes de pentest em uma única plataforma, oferece uma alternativa muito mais econômica em comparação às consultorias especializadas. A geração de relatórios acessíveis e orientações práticas ajuda os usuários a entenderem os riscos e a tomarem decisões informadas. Além disso, seu sistema de autenticação evita que a ferramenta seja utilizada para fins maliciosos.
 
---

#### Criadores de Ganhos: 
O Guardian IA cria valor para seus clientes ao transformar um processo técnico e complexo em uma experiência simplificada e acessível. Sua interface guiada permite que o usuário conduza uma análise completa sem esforço, recebendo relatórios claros, sem jargões técnicos, e com orientações práticas e aplicáveis. A execução dos testes em ambiente isolado garante segurança no processo. Além disso, a constante atualização da IA permite que a ferramenta acompanhe as novas tendências e ameaças do universo da cibersegurança, oferecendo proteção contínua e adaptativa.

---


### 2.1.5. Matriz de Riscos do Projeto (sprint 1)

A matriz de risco é uma ferramenta essencial no gerenciamento de projetos, pois permite identificar, avaliar e priorizar os riscos de maneira visual e sistemática. Ela cruza os eixos de probabilidade e impacto, possibilitando classificar os riscos conforme seu nível de severidade. Isso permite que as equipes direcionem esforços de mitigação para os riscos mais críticos, aumentando a eficiência na gestão do projeto. De acordo com o Project Management Institute (PMI, 2017), a análise de riscos é uma prática indispensável para garantir a previsibilidade, reduzir incertezas e aumentar a probabilidade de sucesso do projeto. No contexto de projetos de cibersegurança, como o desenvolvimento da Guardian IA, a matriz de risco se torna fundamental para antecipar ameaças técnicas, operacionais e legais, contribuindo para um desenvolvimento mais seguro, estruturado e resiliente.

<div align="center" style="margin-bottom: 1em;">
<p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura X</strong> - Matriz de Risco</p>
<img src="../assets/matrizderisco.png"><br />
<p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
    Fonte:Produção Autoral

</p>
</div>


## Riscos:

O desenvolvimento do Guardian IA envolve riscos críticos que precisam ser considerados cuidadosamente. Um dos principais é o risco de uso indevido da plataforma. Mesmo sendo projetada para ser utilizada exclusivamente pelos donos dos sites, existe a possibilidade de que agentes mal-intencionados tentem explorar a ferramenta para realizar testes não autorizados em sistemas de terceiros, o que poderia gerar consequências legais e éticas sérias. Esse cenário reforça a necessidade da implementação de um sistema de autenticação robusto e verificação de propriedade do domínio, como uma camada essencial de proteção.

Outro risco está relacionado às limitações técnicas. A IA depende da integração com ferramentas de pentest como SQLMap, Nmap e outras. Caso essas ferramentas apresentem falhas, erros de leitura ou não sejam atualizadas frequentemente, existe a possibilidade de geração de falsos positivos ou falsos negativos, comprometendo a confiabilidade dos relatórios e das recomendações fornecidas aos usuários.

Adicionalmente, o risco de complexidade na usabilidade também é evidente. Parte do público-alvo pode não ter conhecimento técnico suficiente para interpretar corretamente os resultados, o que poderia gerar insegurança, má interpretação ou até negligência na correção das vulnerabilidades.

Por fim, os riscos legais são igualmente relevantes. Leis como a LGPD no Brasil e o GDPR na Europa impõem restrições severas quanto ao uso de ferramentas que lidam com dados sensíveis ou que possam ser usadas para testar sistemas de terceiros sem consentimento explícito. Portanto, um erro na gestão desses aspectos pode resultar em processos legais, multas e danos irreversíveis à reputação do projeto.

---

## Oportunidades:

O projeto Guardian IA surge em um momento de expansão acelerada das demandas por cibersegurança, oferecendo oportunidades extremamente relevantes. O primeiro ponto de destaque é a capacidade de democratizar o acesso às ferramentas de pentest e segurança digital. Pequenas e médias empresas, que normalmente não possuem recursos para contratar profissionais especializados ou consultorias de segurança, poderão acessar uma solução automatizada, intuitiva e eficiente para identificar e corrigir vulnerabilidades nos seus sites.

Outra grande oportunidade está no crescimento contínuo do mercado de cibersegurança. Relatórios globais apontam que a demanda por soluções de segurança digital cresce anualmente, impulsionada pelo aumento dos ataques cibernéticos e pela transformação digital das empresas. Isso posiciona o Guardian IA como uma solução estratégica e com alto potencial de escalabilidade.

Além disso, há uma oportunidade educacional evidente. A ferramenta pode atuar não apenas como um scanner de vulnerabilidades, mas também como um recurso de aprendizado para seus usuários, oferecendo relatórios com explicações didáticas, recomendações práticas e conteúdos que promovem a conscientização sobre boas práticas de segurança cibernética.

Por fim, a constante evolução da IA e das tecnologias de automação abre espaço para o desenvolvimento de funcionalidades adicionais no futuro, como análises preditivas, detecção de ameaças em tempo real e integração com sistemas de gestão de risco corporativos. Isso amplia significativamente o horizonte de crescimento do projeto, consolidando-o como uma solução robusta e inovadora no mercado de cibersegurança.

---

## 2.2. Personas (sprint 1)

*Posicione aqui suas Personas em forma de texto markdown com imagens, ou como imagem de template preenchido. Atualize esta seção ao longo do módulo se necessário.*

## 2.3. User Stories (sprints 1 a 5)

*Posicione aqui a lista de User Stories levantadas para o projeto. Siga o template de User Stories e utilize a mesma referência USXX no roadmap de seu quadro Kanban. Indique todas as User Stories mapeadas, mesmo aquelas que não forem implementadas ao longo do projeto. Não se esqueça de explicar o INVEST das 5 User Stories prioritárias*

*ATUALIZE ESTA SEÇÃO SEMPRE QUE ALGUMA DEMANDA MUDAR EM SEU PROJETO*

*Template de User Story*
Identificação | USXX (troque XX por numeração ordenada das User Stories)
--- | ---
Persona | nome da Persona
User Story | "como (papel/perfil), posso (ação/meta), para (benefício/razão)"
Critério de aceite 1 | CR1: descrever cenário + testes de aceite
Critério de aceite 2 | CR2: descrever cenário + testes de aceite
Critério de aceite ... | CR...
Critérios INVEST | *(Por que é Independente? Por que é Negociável? Por que é Valorosa? Por que é Estimável? Por que é Pequena? Por que é Testável?)*

# <a name="c3"></a>3. Projeto da Aplicação Web (sprints 1 a 4)

## 3.1. Arquitetura (sprints 3 e 4)

*Posicione aqui o diagrama de arquitetura da sua solução de aplicação web. Atualize sempre que necessário*

## 3.2. Wireframes (sprint 2)

*Posicione aqui as imagens do wireframe construído para sua solução e, opcionalmente, o link para acesso (mantenha o link sempre público para visualização)*

## 3.3. Guia de estilos (sprint 3)

*Descreva aqui orientações gerais para o leitor sobre como utilizar os componentes do guia de estilos de sua solução*

### 3.3.1 Cores

*Apresente aqui a paleta de cores, com seus códigos de aplicação e suas respectivas funções*

### 3.3.2 Tipografia

*Apresente aqui a tipografia da solução, com famílias de fontes e suas respectivas funções*

### 3.3.3 Iconografia e imagens 

*(esta subseção é opcional, caso não existam ícones e imagens, apague esta subseção)*

*posicione aqui imagens e textos contendo exemplos padronizados de ícones e imagens, com seus respectivos atributos de aplicação, utilizadas na solução*

## 3.4 Protótipo de alta fidelidade (sprint 3)

*posicione aqui algumas imagens demonstrativas de seu protótipo de alta fidelidade e o link para acesso ao protótipo completo (mantenha o link sempre público para visualização)*

## 3.5. Modelagem do banco de dados (sprints 2 e 4)

### 3.5.1. Modelo relacional (sprints 2 e 4)

*posicione aqui os diagramas de modelos relacionais do seu banco de dados, apresentando todos os esquemas de tabelas e suas relações. Utilize texto para complementar suas explicações, se necessário* 

### 3.5.2. Consultas SQL e lógica proposicional (sprint 2)

*posicione aqui uma lista de consultas SQL compostas, realizadas pelo back-end da aplicação web, com sua respectiva lógica proposicional, descrita conforme template abaixo. Lembre-se que para usar LaTeX em markdown, basta você colocar as expressões entre $ ou $$*

*Template de SQL + lógica proposicional*
#1 | ---
--- | ---
**Expressão SQL** | SELECT * FROM suppliers WHERE (state = 'California' AND supplier_id <> 900) OR (supplier_id = 100); 
**Proposições lógicas** | $A$: O estado é 'California' (state = 'California') <br> $B$: O ID do fornecedor não é 900 (supplier_id ≠ 900) <br> $C$: O ID do fornecedor é 100 (supplier_id = 100)
**Expressão lógica proposicional** | $(A \land B) \lor C$
**Tabela Verdade** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$(A \land B)$</th> <th>$(A \land B) \lor C$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table>

*Dica: edite a tabela verdade fora do markdown, para ter melhor controle*

## 3.6. WebAPI e endpoints (sprints 3 e 4)

*Utilize um link para outra página de documentação contendo a descrição completa de cada endpoint. Ou descreva aqui cada endpoint criado para seu sistema.* 

*Cada endpoint deve conter endereço, método (GET, POST, PUT, PATCH, DELETE), header, body e formatos de response*

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

## 4.1. Primeira versão da aplicação web (sprint 3)

*Descreva e ilustre aqui o desenvolvimento da sua primeira versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

## 4.2. Segunda versão da aplicação web (sprint 4)

*Descreva e ilustre aqui o desenvolvimento da sua segunda versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

## 4.3. Versão final da aplicação web (sprint 5)

*Descreva e ilustre aqui o desenvolvimento da última versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados (sprint 4)

*Liste e descreva os testes unitários dos endpoints criados, automatizados e planejados para sua solução. Posicione aqui também o relatório de cobertura de testes Jest se houver (através de link ou transcrito para estrutura markdown)*

## 5.2. Testes de usabilidade (sprint 5)

*Posicione aqui as tabelas com enunciados de tarefas, etapas e resultados de testes de usabilidade. Ou utilize um link para seu relatório de testes (mantenha o link sempre público para visualização)*

# <a name="c6"></a>6. Estudo de Mercado e Plano de Marketing (sprint 4)

## 6.1 Resumo Executivo

*Preencher com até 300 palavras, sem necessidade de fonte*

*Apresente de forma clara e objetiva os principais destaques do projeto: oportunidades de mercado, diferenciais competitivos da aplicação web e os objetivos estratégicos pretendidos.*

## 6.2 Análise de Mercado

*a) Visão Geral do Setor (até 250 palavras)*
*Contextualize o setor no qual a aplicação está inserida, considerando aspectos econômicos, tecnológicos e regulatórios. Utilize fontes confiáveis.*

*b) Tamanho e Crescimento do Mercado (até 250 palavras)*
*Apresente dados quantitativos sobre o tamanho atual e projeções de crescimento do mercado. Utilize fontes confiáveis.*

*c) Tendências de Mercado (até 300 palavras)*
*Identifique e analise tendências relevantes (tecnológicas, comportamentais e mercadológicas) que influenciam o setor. Utilize fontes confiáveis.*

## 6.3 Análise da Concorrência

*a) Principais Concorrentes (até 250 palavras)*
*Liste os concorrentes diretos e indiretos, destacando suas principais características e posicionamento no mercado.*

*b) Vantagens Competitivas da Aplicação Web (até 250 palavras)*
*Descreva os diferenciais da sua aplicação em relação aos concorrentes, sem necessidade de citação de fontes.*


## 6.4 Público-Alvo

*a) Segmentação de Mercado (até 250 palavras)*
Descreva os principais segmentos de mercado a serem atendidos pela aplicação. Utilize bases de dados e fontes confiáveis.*

*b) Perfil do Público-Alvo (até 250 palavras)*
*Caracterize o público-alvo com dados demográficos, psicográficos e comportamentais, incluindo necessidades específicas. Utilize fontes obrigatórias.*


## 6.5 Posicionamento

*a) Proposta de Valor Única (até 250 palavras)*
*Defina de maneira clara o que torna a sua aplicação única e valiosa para o mercado.*

*b) Estratégia de Diferenciação (até 250 palavras)*
*Explique como sua aplicação se destacará da concorrência, evidenciando a lógica por trás do posicionamento.*

## 6.6 Estratégia de Marketing 

*a) Produto/Serviço (até 200 palavras)*
*Descreva as funcionalidades, benefícios e diferenciais da aplicação*

*6.2 Preço (até 200 palavras)*
*Explique o modelo de precificação adotado e justifique com base nas análises anteriores.*

*6.3 Praça (Distribuição) (até 200 palavras)*
*Apresente os canais digitais utilizados para distribuir e entregar a aplicação ao público.*

*6.4 Promoção (até 200 palavras)*
*Descreva as estratégias digitais planejadas, como SEO, redes sociais, marketing de conteúdo e campanhas pagas.*

# <a name="c7"></a>7. Conclusões e trabalhos futuros (sprint 5)

*Escreva de que formas a solução da aplicação web atingiu os objetivos descritos na seção 2 deste documento. Indique pontos fortes e pontos a melhorar de maneira geral.*

*Relacione os pontos de melhorias evidenciados nos testes com planos de ações para serem implementadas. O grupo não precisa implementá-las, pode deixar registrado aqui o plano para ações futuras*

*Relacione também quaisquer outras ideias que o grupo tenha para melhorias futuras*

# <a name="c8"></a>8. Referências (sprints 1 a 5)

Osterwalder, A., Pigneur, Y., Bernarda, G., & Smith, A. (2014). Value Proposition Design: How to Create Products and Services Customers Want. John Wiley & Sons.

Project Management Institute (PMI). (2017). A Guide to the Project Management Body of Knowledge (PMBOK® Guide) – Sixth Edition. Project Management Institute.

# <a name="c9"></a>Anexos


