# src/phases/fase5_pos_exploracao.py - Fase 5: Pós-Exploração e Análise de Movimento Lateral

import os
import json
import re
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from urllib.parse import urlparse

# Imports da nova estrutura
from ..core.logger import get_logger
from ..core.config import DEFAULT_POS_EXPLORACAO_CONFIG
from ..utils.file_utils import save_json, load_json, save_csv
from ..utils.command_utils import CommandExecutor

@dataclass
class PosExploracaoConfig:
    """Configurações para análise pós-exploração"""
    analise_profunda: bool = True
    incluir_cenarios_hipoteticos: bool = True
    avaliar_movimento_lateral: bool = True

@dataclass
class CenarioEscalacao:
    """Cenário de escalação de privilégios"""
    vulnerability_id: str
    cenario_tipo: str
    probabilidade: str  # LOW, MEDIUM, HIGH
    impacto_potencial: str
    descricao_cenario: str
    vetores_escalacao: List[str]
    contramedidas: List[str]
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

@dataclass
class AnaliseMovimentoLateral:
    """Análise de movimento lateral"""
    dominio_origem: str
    dominios_relacionados: List[str]
    vetores_movimento: List[str]
    recursos_acessiveis: List[str]
    impacto_rede: str
    recomendacoes_segmentacao: List[str]

class AnalisadorPosExploracao:
    """Classe principal para análise pós-exploração"""
    
    def __init__(self, alvo: str, diretorio_saida: str, config: PosExploracaoConfig = None):
        self.alvo = alvo
        self.diretorio_saida = Path(diretorio_saida)
        self.config = config or PosExploracaoConfig()
        self.exploits_dados = []
        self.cenarios_escalacao = []
        self.analises_movimento = []
        self._criar_estrutura_diretorios()
        self.logger = self._configurar_logging()
        self._carregar_dados_exploracao()
    
    def _configurar_logging(self):
        """Configura logging detalhado"""
        self.logger = get_logger(f"pos_exploit_{self.alvo}")
        return self.logger
    
    def _criar_estrutura_diretorios(self):
        """Cria estrutura de diretórios para a Fase 5"""
        subdirs = [
            "escalacao_privilegios", "movimento_lateral", "analise_cenarios",
            "mapeamento_rede", "relatorios_fase5"
        ]
        for subdir in subdirs:
            (self.diretorio_saida / subdir).mkdir(exist_ok=True, parents=True)
    
    def _carregar_dados_exploracao(self):
        """Carrega dados da Fase 4"""
        arquivo_exploits = None
        
        # Procura pelo arquivo mais recente da Fase 4
        relatorios_dir = self.diretorio_saida / "relatorios_fase4"
        if relatorios_dir.exists():
            arquivos = list(relatorios_dir.glob("exploracao_*.json"))
            if arquivos:
                arquivo_exploits = max(arquivos, key=os.path.getctime)
        
        if arquivo_exploits and arquivo_exploits.exists():
            try:
                with open(arquivo_exploits, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.exploits_dados = data.get('exploits', [])
                self.logger.info(f"Carregados {len(self.exploits_dados)} exploits da Fase 4")
            except Exception as e:
                self.logger.error(f"Erro ao carregar dados de exploração: {e}")
                self.exploits_dados = []
        else:
            self.logger.warning("Nenhum dado de exploração encontrado da Fase 4")
            self.exploits_dados = []

    def analise_escalacao_privilegios(self) -> List[CenarioEscalacao]:
        """Analisa cenários de escalação de privilégios"""
        self.logger.info("🔍 Analisando cenários de escalação de privilégios...")
        
        cenarios = []
        
        for exploit in self.exploits_dados:
            exploit_type = exploit.get('exploit_type', '')
            target_url = exploit.get('target_url', '')
            
            # Análise específica por tipo de vulnerabilidade
            if 'SQL Injection' in exploit_type:
                cenario = self._analisar_escalacao_sqli(exploit)
                if cenario:
                    cenarios.append(cenario)
            
            elif 'XSS' in exploit_type:
                cenario = self._analisar_escalacao_xss(exploit)
                if cenario:
                    cenarios.append(cenario)
            
            elif 'Component' in exploit_type:
                cenario = self._analisar_escalacao_componente(exploit)
                if cenario:
                    cenarios.append(cenario)
        
        self.logger.info(f"✅ Identificados {len(cenarios)} cenários de escalação")
        return cenarios

    def _analisar_escalacao_sqli(self, exploit: Dict) -> Optional[CenarioEscalacao]:
        """Analisa escalação para SQL Injection"""
        url = exploit.get('target_url', '')
        
        # Determina contexto baseado na URL
        if any(keyword in url.lower() for keyword in ['admin', 'login', 'auth']):
            return CenarioEscalacao(
                vulnerability_id=exploit.get('vulnerability_id', ''),
                cenario_tipo="SQL Injection - Acesso Administrativo",
                probabilidade="HIGH",
                impacto_potencial="CRITICAL",
                descricao_cenario="""
                CENÁRIO DE ESCALAÇÃO - SQL INJECTION EM ÁREA ADMINISTRATIVA
                
                1. ACESSO INICIAL: Exploração da SQL Injection para bypass de autenticação
                2. ESCALAÇÃO: Acesso a painéis administrativos e funções privilegiadas
                3. PERSISTÊNCIA: Criação de contas administrativas backdoor
                4. MOVIMENTO: Acesso a outros sistemas através de credenciais obtidas
                
                IMPACTO: Controle total da aplicação e possível acesso a sistemas internos
                """,
                vetores_escalacao=[
                    "Bypass de autenticação via SQL Injection",
                    "Extração de hashes de senhas administrativas",
                    "Acesso a tabelas de configuração do sistema",
                    "Possível execução de comandos no servidor de banco"
                ],
                contramedidas=[
                    "Implementar prepared statements imediatamente",
                    "Aplicar princípio do menor privilégio no banco",
                    "Segregar usuários administrativos",
                    "Implementar autenticação multifator",
                    "Monitoramento de queries suspeitas"
                ]
            )
        else:
            return CenarioEscalacao(
                vulnerability_id=exploit.get('vulnerability_id', ''),
                cenario_tipo="SQL Injection - Extração de Dados",
                probabilidade="MEDIUM",
                impacto_potencial="HIGH",
                descricao_cenario="""
                CENÁRIO DE ESCALAÇÃO - SQL INJECTION EM APLICAÇÃO
                
                1. RECONHECIMENTO: Mapeamento da estrutura do banco de dados
                2. EXTRAÇÃO: Download de dados sensíveis (usuários, senhas, etc.)
                3. ANÁLISE: Quebra de hashes e identificação de credenciais reutilizadas
                4. ESCALAÇÃO: Uso de credenciais em outros sistemas da organização
                
                IMPACTO: Exposição de dados e possível acesso a outros sistemas
                """,
                vetores_escalacao=[
                    "Enumeração de tabelas e colunas",
                    "Extração de dados de usuários",
                    "Análise de credenciais para reutilização",
                    "Tentativa de acesso a outros serviços"
                ],
                contramedidas=[
                    "Correção imediata da SQL Injection",
                    "Criptografia forte para senhas",
                    "Política de senhas únicas",
                    "Monitoramento de tentativas de login"
                ]
            )

    def _analisar_escalacao_xss(self, exploit: Dict) -> Optional[CenarioEscalacao]:
        """Analisa escalação para XSS"""
        return CenarioEscalacao(
            vulnerability_id=exploit.get('vulnerability_id', ''),
            cenario_tipo="XSS - Roubo de Sessão e Phishing",
            probabilidade="MEDIUM",
            impacto_potencial="MEDIUM",
            descricao_cenario="""
            CENÁRIO DE ESCALAÇÃO - CROSS-SITE SCRIPTING
            
            1. EXPLORAÇÃO: Injeção de JavaScript malicioso na aplicação
            2. COLETA: Roubo de cookies e tokens de sessão de usuários
            3. PERSONIFICAÇÃO: Acesso às contas dos usuários comprometidos
            4. ESCALAÇÃO: Busca por usuários com privilégios elevados
            
            IMPACTO: Comprometimento de contas de usuários e possível acesso administrativo
            """,
            vetores_escalacao=[
                "Injeção de payloads de roubo de cookies",
                "Redirecionamento para páginas de phishing",
                "Keylogging através de JavaScript",
                "Execução de ações em nome do usuário"
            ],
            contramedidas=[
                "Implementar Content Security Policy (CSP)",
                "Sanitização de todas as saídas",
                "Validação rigorosa de entradas",
                "Tokens anti-CSRF",
                "Educação de usuários sobre phishing"
            ]
        )

    def _analisar_escalacao_componente(self, exploit: Dict) -> Optional[CenarioEscalacao]:
        """Analisa escalação para componentes vulneráveis"""
        return CenarioEscalacao(
            vulnerability_id=exploit.get('vulnerability_id', ''),
            cenario_tipo="Componente Vulnerável - Exploração Remota",
            probabilidade="MEDIUM",
            impacto_potencial="HIGH",
            descricao_cenario="""
            CENÁRIO DE ESCALAÇÃO - COMPONENTE VULNERÁVEL
            
            1. IDENTIFICAÇÃO: Componente com vulnerabilidade conhecida (CVE)
            2. EXPLORAÇÃO: Uso de exploits públicos disponíveis
            3. ACESSO: Obtenção de shell ou acesso ao sistema
            4. ESCALAÇÃO: Busca por vulnerabilidades locais para root/admin
            
            IMPACTO: Possível comprometimento completo do servidor
            """,
            vetores_escalacao=[
                "Exploits públicos para a vulnerabilidade identificada",
                "Escalação de privilégios local",
                "Acesso a arquivos de configuração",
                "Movimento lateral na rede interna"
            ],
            contramedidas=[
                "Atualização imediata do componente",
                "Aplicação de patches de segurança",
                "Monitoramento de tentativas de exploração",
                "Segmentação de rede",
                "Princípio do menor privilégio"
            ]
        )

    def analise_movimento_lateral(self) -> List[AnaliseMovimentoLateral]:
        """Analisa possibilidades de movimento lateral"""
        self.logger.info("🔍 Analisando movimento lateral...")
        
        # Agrupa exploits por domínio
        dominios_comprometidos = {}
        for exploit in self.exploits_dados:
            url = exploit.get('target_url', '')
            dominio = urlparse(url).netloc
            if dominio not in dominios_comprometidos:
                dominios_comprometidos[dominio] = []
            dominios_comprometidos[dominio].append(exploit)
        
        analises = []
        for dominio, exploits in dominios_comprometidos.items():
            analise = self._analisar_movimento_dominio(dominio, exploits)
            analises.append(analise)
        
        self.logger.info(f"✅ Analisados {len(analises)} domínios para movimento lateral")
        return analises

    def _analisar_movimento_dominio(self, dominio: str, exploits: List[Dict]) -> AnaliseMovimentoLateral:
        """Analisa movimento lateral para um domínio específico"""
        
        # Identifica subdomínios relacionados
        dominios_relacionados = []
        if '.' in dominio:
            partes = dominio.split('.')
            if len(partes) > 2:
                dominio_principal = '.'.join(partes[-2:])
                dominios_relacionados.append(f"*.{dominio_principal}")
        
        # Determina vetores de movimento baseado nos exploits
        vetores = []
        recursos = []
        
        for exploit in exploits:
            exploit_type = exploit.get('exploit_type', '')
            
            if 'SQL Injection' in exploit_type:
                vetores.extend([
                    "Credenciais extraídas do banco de dados",
                    "Configurações de conexão com outros sistemas",
                    "Informações de infraestrutura interna"
                ])
                recursos.extend([
                    "Banco de dados corporativo",
                    "Sistemas de autenticação centralizados",
                    "Aplicações que compartilham credenciais"
                ])
            
            elif 'XSS' in exploit_type:
                vetores.extend([
                    "Sessões de usuários administrativos",
                    "Tokens de acesso a APIs internas",
                    "Credenciais inseridas em formulários"
                ])
                recursos.extend([
                    "Painéis administrativos",
                    "APIs internas",
                    "Sistemas de gestão"
                ])
        
        # Remove duplicatas
        vetores = list(set(vetores))
        recursos = list(set(recursos))
        
        return AnaliseMovimentoLateral(
            dominio_origem=dominio,
            dominios_relacionados=dominios_relacionados,
            vetores_movimento=vetores,
            recursos_acessiveis=recursos,
            impacto_rede="MEDIUM" if len(exploits) == 1 else "HIGH",
            recomendacoes_segmentacao=[
                "Implementar segmentação de rede por VLANs",
                "Aplicar princípio do menor privilégio",
                "Monitoramento de tráfego lateral",
                "Autenticação multifator para sistemas críticos",
                "Rotação regular de credenciais"
            ]
        )

    def gerar_matriz_risco_pos_exploracao(self) -> Dict:
        """Gera matriz de risco pós-exploração"""
        self.logger.info("📊 Gerando matriz de risco pós-exploração...")
        
        riscos_por_categoria = {
            "Escalação de Privilégios": {
                "probabilidade": "MEDIUM",
                "impacto": "HIGH",
                "cenarios": len([c for c in self.cenarios_escalacao if c.probabilidade in ["MEDIUM", "HIGH"]])
            },
            "Movimento Lateral": {
                "probabilidade": "LOW",
                "impacto": "MEDIUM", 
                "cenarios": len(self.analises_movimento)
            },
            "Persistência": {
                "probabilidade": "MEDIUM",
                "impacto": "HIGH",
                "cenarios": len([e for e in self.exploits_dados if e.get('impact_level') in ["HIGH", "CRITICAL"]])
            },
            "Exfiltração de Dados": {
                "probabilidade": "HIGH",
                "impacto": "CRITICAL",
                "cenarios": len([e for e in self.exploits_dados if "SQL Injection" in e.get('exploit_type', '')])
            }
        }
        
        return riscos_por_categoria

    def executar_fase_completa(self) -> Dict:
        """Executa o fluxo completo da Fase 5"""
        inicio = datetime.now()
        try:
            self.logger.info(f"🚀 INICIANDO FASE 5: PÓS-EXPLORAÇÃO E MOVIMENTO LATERAL PARA {self.alvo}")
            self.logger.info(f"🎯 Exploits para análise: {len(self.exploits_dados)}")
            
            if not self.exploits_dados:
                self.logger.warning("⚠️ Nenhum exploit encontrado para análise pós-exploração")
                return {'sucesso': False, 'erro': 'Nenhum exploit para analisar'}
            
            # Executa análises
            self.cenarios_escalacao = self.analise_escalacao_privilegios()
            self.analises_movimento = self.analise_movimento_lateral()
            matriz_risco = self.gerar_matriz_risco_pos_exploracao()
            
            # Gera relatório
            self._gerar_relatorio_fase5(matriz_risco)
            
            duracao = datetime.now() - inicio
            self.logger.info("="*60)
            self.logger.info("🎉 FASE 5 CONCLUÍDA COM SUCESSO!")
            self.logger.info(f"📈 Cenários de escalação identificados: {len(self.cenarios_escalacao)}")
            self.logger.info(f"🔄 Análises de movimento lateral: {len(self.analises_movimento)}")
            self.logger.info(f"⏱️  Duração total: {duracao}")
            
            return {
                'sucesso': True,
                'cenarios_escalacao': len(self.cenarios_escalacao),
                'analises_movimento': len(self.analises_movimento),
                'duracao': str(duracao)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erro crítico na Fase 5: {str(e)}", exc_info=True)
            return {'sucesso': False, 'erro': str(e)}

    def _gerar_relatorio_fase5(self, matriz_risco: Dict):
        """Gera relatório detalhado da Fase 5"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        relatorio_json = {
            "alvo": self.alvo,
            "timestamp": timestamp,
            "cenarios_escalacao": [asdict(c) for c in self.cenarios_escalacao],
            "analises_movimento_lateral": [asdict(a) for a in self.analises_movimento],
            "matriz_risco_pos_exploracao": matriz_risco,
            "resumo_executivo": {
                "total_cenarios_escalacao": len(self.cenarios_escalacao),
                "dominios_analisados": len(self.analises_movimento),
                "nivel_risco_geral": self._calcular_risco_geral(),
                "recomendacoes_prioritarias": self._gerar_recomendacoes_prioritarias()
            }
        }
        
        arquivo_json = self.diretorio_saida / "relatorios_fase5" / f"pos_exploracao_{timestamp}.json"
        with open(arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(relatorio_json, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📋 Relatório pós-exploração salvo: {arquivo_json}")

    def _calcular_risco_geral(self) -> str:
        """Calcula nível de risco geral"""
        cenarios_altos = len([c for c in self.cenarios_escalacao if c.probabilidade == "HIGH"])
        exploits_criticos = len([e for e in self.exploits_dados if e.get('impact_level') == "CRITICAL"])
        
        if cenarios_altos > 0 or exploits_criticos > 0:
            return "ALTO"
        elif len(self.cenarios_escalacao) > 2:
            return "MÉDIO"
        else:
            return "BAIXO"

    def _gerar_recomendacoes_prioritarias(self) -> List[str]:
        """Gera recomendações prioritárias"""
        return [
            "Correção imediata de vulnerabilidades críticas e altas",
            "Implementação de segmentação de rede",
            "Fortalecimento da autenticação (MFA)",
            "Monitoramento contínuo de atividades suspeitas",
            "Revisão e atualização de políticas de segurança",
            "Treinamento da equipe em resposta a incidentes"
        ]

def run(alvo: str, diretorio_saida: str, config_dict: Dict = None):
    """Interface principal para executar a Fase 5"""
    config = PosExploracaoConfig(**config_dict) if config_dict else PosExploracaoConfig()
    analisador = AnalisadorPosExploracao(alvo, diretorio_saida, config)
    return analisador.executar_fase_completa()
