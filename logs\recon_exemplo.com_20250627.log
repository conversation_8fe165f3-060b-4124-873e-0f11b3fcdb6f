2025-06-27 14:33:11 - guardian.recon_exemplo.com - INFO - 🚀 INICIANDO FASE 1: RECONHECIMENTO ESTRATÉGICO PARA O ALVO: exemplo.com
2025-06-27 14:33:11 - guardian.recon_exemplo.com - INFO - 🔍 Iniciando enumeração passiva de subdomínios...
2025-06-27 14:33:11 - guardian.recon_exemplo.com - INFO - Executando: subfinder -d exemplo.com -silent
2025-06-27 14:33:11 - guardian.recon_exemplo.com - ERROR - Erro inesperado em subfinder: 'dict' object has no attribute 'timeout'
2025-06-27 14:33:11 - guardian.recon_exemplo.com - INFO - Executando: amass enum -passive -d exemplo.com
2025-06-27 14:33:11 - guardian.recon_exemplo.com - ERROR - Erro inesperado em amass: 'dict' object has no attribute 'timeout'
2025-06-27 14:33:11 - guardian.recon_exemplo.com - INFO - Executando: assetfinder --subs-only exemplo.com
2025-06-27 14:33:11 - guardian.recon_exemplo.com - ERROR - Erro inesperado em assetfinder: 'dict' object has no attribute 'timeout'
2025-06-27 14:33:11 - guardian.recon_exemplo.com - INFO - Executando: findomain -t exemplo.com -q
2025-06-27 14:33:11 - guardian.recon_exemplo.com - ERROR - Erro inesperado em findomain: 'dict' object has no attribute 'timeout'
2025-06-27 14:33:12 - guardian.recon_exemplo.com - INFO -   crt.sh: 4 subdomínios encontrados.
2025-06-27 14:33:12 - guardian.recon_exemplo.com - INFO - ✅ Enumeração passiva concluída: 4 subdomínios únicos
2025-06-27 14:33:12 - guardian.recon_exemplo.com - ERROR - ❌ Erro crítico na Fase 1: 'dict' object has no attribute 'use_passive_only'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\GuardianIA\src\phases\fase1_recon.py", line 265, in executar_fase_completa
    subdominios_ativos_bruteforce = self.enumeracao_subdominios_ativa(subdominios_passivos)
  File "C:\Users\<USER>\Documents\GitHub\GuardianIA\src\phases\fase1_recon.py", line 151, in enumeracao_subdominios_ativa
    if self.config.use_passive_only: return set()
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'use_passive_only'
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - 🚀 INICIANDO FASE 1: RECONHECIMENTO ESTRATÉGICO PARA O ALVO: exemplo.com
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - 🔍 Iniciando enumeração passiva de subdomínios...
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - Executando: subfinder -d exemplo.com -silent
2025-06-27 14:38:08 - guardian.recon_exemplo.com - ERROR - Ferramenta subfinder não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - Executando: amass enum -passive -d exemplo.com
2025-06-27 14:38:08 - guardian.recon_exemplo.com - ERROR - Ferramenta amass não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - Executando: assetfinder --subs-only exemplo.com
2025-06-27 14:38:08 - guardian.recon_exemplo.com - ERROR - Ferramenta assetfinder não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - Executando: findomain -t exemplo.com -q
2025-06-27 14:38:08 - guardian.recon_exemplo.com - ERROR - Ferramenta findomain não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:08 - guardian.recon_exemplo.com - ERROR - Erro ao consultar crt.sh: ('Connection aborted.', ConnectionResetError(10054, 'Foi forçado o cancelamento de uma conexão existente pelo host remoto', None, 10054, None))
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - ✅ Enumeração passiva concluída: 0 subdomínios únicos
2025-06-27 14:38:08 - guardian.recon_exemplo.com - INFO - 🔨 Iniciando enumeração ativa de subdomínios...
2025-06-27 14:38:08 - guardian.recon_exemplo.com - ERROR - ❌ Erro crítico na Fase 1: 'ReconConfig' object has no attribute 'wordlists'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\GuardianIA\src\phases\fase1_recon.py", line 265, in executar_fase_completa
    subdominios_ativos_bruteforce = self.enumeracao_subdominios_ativa(subdominios_passivos)
  File "C:\Users\<USER>\Documents\GitHub\GuardianIA\src\phases\fase1_recon.py", line 154, in enumeracao_subdominios_ativa
    for wordlist in self.config.wordlists:
                    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReconConfig' object has no attribute 'wordlists'
2025-06-27 14:38:55 - guardian.recon_exemplo.com - INFO - 🚀 INICIANDO FASE 1: RECONHECIMENTO ESTRATÉGICO PARA O ALVO: exemplo.com
2025-06-27 14:38:55 - guardian.recon_exemplo.com - INFO - 🔍 Iniciando enumeração passiva de subdomínios...
2025-06-27 14:38:55 - guardian.recon_exemplo.com - INFO - Executando: subfinder -d exemplo.com -silent
2025-06-27 14:38:55 - guardian.recon_exemplo.com - ERROR - Ferramenta subfinder não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:55 - guardian.recon_exemplo.com - INFO - Executando: amass enum -passive -d exemplo.com
2025-06-27 14:38:55 - guardian.recon_exemplo.com - ERROR - Ferramenta amass não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:55 - guardian.recon_exemplo.com - INFO - Executando: assetfinder --subs-only exemplo.com
2025-06-27 14:38:55 - guardian.recon_exemplo.com - ERROR - Ferramenta assetfinder não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:55 - guardian.recon_exemplo.com - INFO - Executando: findomain -t exemplo.com -q
2025-06-27 14:38:55 - guardian.recon_exemplo.com - ERROR - Ferramenta findomain não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - ✅ Enumeração passiva concluída: 0 subdomínios únicos
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - 🔨 Iniciando enumeração ativa de subdomínios...
2025-06-27 14:38:56 - guardian.recon_exemplo.com - WARNING - Wordlist não encontrada: /usr/share/wordlists/SecLists/Discovery/DNS/subdomains-top1million-5000.txt
2025-06-27 14:38:56 - guardian.recon_exemplo.com - WARNING - Wordlist não encontrada: /usr/share/wordlists/SecLists/Discovery/DNS/fierce-hostlist.txt
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - ✅ Enumeração ativa concluída: 0 novos subdomínios
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - 🌐 Validando e enriquecendo 0 subdomínios...
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - Executando: httpx -l C:\Users\<USER>\Documents\GitHub\GuardianIA\results\exemplo_com\subdominios\para_validacao.txt -status-code -title -tech-detect -server -content-length -response-time -location -cdn -ip -threads 30 -timeout 60 -json -silent
2025-06-27 14:38:56 - guardian.recon_exemplo.com - ERROR - Ferramenta httpx não encontrada. Verifique se está instalada e no PATH.
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - ✅ Validação concluída: 0 subdomínios ativos
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - 🔧 Analisando tecnologias identificadas...
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - 📋 Gerando relatório da Fase 1...
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - ============================================================
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - 🎉 FASE 1 CONCLUÍDA COM SUCESSO!
2025-06-27 14:38:56 - guardian.recon_exemplo.com - INFO - ⏱️  Duração total: 0:00:01.135309
