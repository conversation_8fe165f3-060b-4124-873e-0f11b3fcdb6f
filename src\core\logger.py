# src/core/logger.py - Sistema de logging centralizado

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional
from .config import DEFAULT_CONFIG

class GuardianLogger:
    """Sistema de logging centralizado para o Guardian IA"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, name: str, target: Optional[str] = None) -> logging.Logger:
        """Obtém ou cria um logger específico"""
        
        # Cria chave única para o logger
        logger_key = f"{name}_{target}" if target else name
        
        if logger_key not in cls._loggers:
            cls._loggers[logger_key] = cls._create_logger(name, target)
        
        return cls._loggers[logger_key]
    
    @classmethod
    def _create_logger(cls, name: str, target: Optional[str] = None) -> logging.Logger:
        """Cria um novo logger configurado"""
        
        logger = logging.getLogger(logger_key := f"guardian.{name}")
        logger.setLevel(getattr(logging, DEFAULT_CONFIG.log_level))
        
        # Remove handlers existentes para evitar duplicação
        logger.handlers.clear()
        
        # Formatter para arquivos
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Formatter para console
        console_formatter = logging.Formatter(
            '[%(name)s - %(levelname)s] %(message)s'
        )
        
        # Handler para arquivo
        if target:
            log_dir = DEFAULT_CONFIG.logs_dir / target.replace(".", "_")
            log_dir.mkdir(exist_ok=True, parents=True)
            log_file = log_dir / f"{name}_{datetime.now().strftime('%Y%m%d')}.log"
        else:
            log_file = DEFAULT_CONFIG.logs_dir / f"{name}_{datetime.now().strftime('%Y%m%d')}.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Handler para console
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    @classmethod
    def setup_phase_logger(cls, phase_name: str, target: str) -> logging.Logger:
        """Configura logger específico para uma fase"""
        return cls.get_logger(phase_name, target)
    
    @classmethod
    def log_phase_start(cls, logger: logging.Logger, phase_name: str, target: str):
        """Log padronizado para início de fase"""
        logger.info("=" * 60)
        logger.info(f"🚀 INICIANDO {phase_name.upper()} PARA {target}")
        logger.info("=" * 60)
    
    @classmethod
    def log_phase_end(cls, logger: logging.Logger, phase_name: str, duration: str, success: bool = True):
        """Log padronizado para fim de fase"""
        status = "✅ CONCLUÍDA" if success else "❌ FALHOU"
        logger.info("=" * 60)
        logger.info(f"{status} - {phase_name.upper()}")
        logger.info(f"⏱️  Duração: {duration}")
        logger.info("=" * 60)
    
    @classmethod
    def log_tool_execution(cls, logger: logging.Logger, tool_name: str, command: str, success: bool = True):
        """Log padronizado para execução de ferramentas"""
        status = "✅" if success else "❌"
        logger.info(f"{status} {tool_name}: {command}")
    
    @classmethod
    def log_vulnerability_found(cls, logger: logging.Logger, vuln_type: str, url: str, severity: str):
        """Log padronizado para vulnerabilidades encontradas"""
        severity_emoji = {
            "critical": "🔴",
            "high": "🟠", 
            "medium": "🟡",
            "low": "🟢",
            "info": "🔵"
        }
        emoji = severity_emoji.get(severity.lower(), "⚪")
        logger.warning(f"{emoji} VULNERABILIDADE: {vuln_type} em {url} (Severidade: {severity})")
    
    @classmethod
    def log_error_with_context(cls, logger: logging.Logger, error: Exception, context: str):
        """Log padronizado para erros com contexto"""
        logger.error(f"❌ ERRO em {context}: {str(error)}")
        logger.debug(f"Detalhes do erro: {error}", exc_info=True)

# Função de conveniência para obter logger
def get_logger(name: str, target: Optional[str] = None) -> logging.Logger:
    """Função de conveniência para obter um logger"""
    return GuardianLogger.get_logger(name, target)
