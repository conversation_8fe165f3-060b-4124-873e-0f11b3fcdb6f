#!/usr/bin/env python3
# setup_guardian.py - Script de configuração do Guardian IA

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Exibe banner do Guardian IA"""
    print("="*80)
    print("🛡️  GUARDIAN IA - SETUP E CONFIGURAÇÃO")
    print("   'Protegendo o invisível. Antecipando o inevitável.'")
    print("="*80)

def check_python_version():
    """Verifica versão do Python"""
    print("🐍 Verificando versão do Python...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ é necessário. Versão atual:", f"{version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
    return True

def install_python_dependencies():
    """Instala dependências Python"""
    print("\n📦 Instalando dependências Python...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        print("❌ Arquivo requirements.txt não encontrado!")
        return False
    
    try:
        # Atualiza pip primeiro
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Instala dependências
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                               check=True, capture_output=True, text=True)
        print("✅ Dependências Python instaladas com sucesso!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        print(f"Saída do erro: {e.stderr}")
        return False

def check_go_tools():
    """Verifica se as ferramentas Go estão instaladas"""
    print("\n🔧 Verificando ferramentas Go...")
    
    tools = {
        "subfinder": "github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest",
        "httpx": "github.com/projectdiscovery/httpx/cmd/httpx@latest", 
        "nuclei": "github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest",
        "gobuster": "github.com/OJ/gobuster/v3@latest"
    }
    
    missing_tools = []
    
    for tool, install_path in tools.items():
        try:
            result = subprocess.run([tool, "-version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {tool} - Instalado")
            else:
                missing_tools.append((tool, install_path))
        except FileNotFoundError:
            missing_tools.append((tool, install_path))
            print(f"❌ {tool} - Não encontrado")
    
    return missing_tools

def install_go_tools(missing_tools):
    """Instala ferramentas Go em falta"""
    if not missing_tools:
        return True
    
    print(f"\n🔧 Instalando {len(missing_tools)} ferramentas Go...")
    
    # Verifica se Go está instalado
    try:
        subprocess.run(["go", "version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Go não está instalado. Instale Go primeiro: https://golang.org/dl/")
        return False
    
    for tool, install_path in missing_tools:
        print(f"📥 Instalando {tool}...")
        try:
            subprocess.run(["go", "install", "-v", install_path], check=True, capture_output=True)
            print(f"✅ {tool} instalado com sucesso!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar {tool}: {e}")
            return False
    
    return True

def create_directories():
    """Cria diretórios necessários"""
    print("\n📁 Criando estrutura de diretórios...")
    
    base_dir = Path(__file__).parent
    directories = [
        "resultados",
        "logs", 
        "wordlists",
        "config"
    ]
    
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(exist_ok=True)
        print(f"✅ Diretório criado: {directory}")

def download_wordlists():
    """Baixa wordlists essenciais"""
    print("\n📝 Configurando wordlists...")
    
    wordlists_dir = Path(__file__).parent / "wordlists"
    
    # Lista de wordlists essenciais
    wordlists = {
        "common.txt": [
            "admin", "api", "app", "assets", "backup", "blog", "cdn", "config",
            "dashboard", "dev", "docs", "ftp", "git", "help", "images", "login",
            "mail", "mobile", "news", "old", "panel", "portal", "secure", "shop",
            "staging", "static", "support", "test", "upload", "user", "www"
        ],
        "directories.txt": [
            "admin", "administrator", "api", "app", "application", "apps", "archive",
            "backup", "backups", "bin", "blog", "cache", "cgi-bin", "config", 
            "content", "css", "data", "database", "db", "dev", "development",
            "doc", "docs", "download", "downloads", "etc", "files", "ftp",
            "images", "img", "include", "includes", "js", "lib", "library",
            "log", "logs", "mail", "media", "old", "private", "public", "src",
            "static", "temp", "tmp", "upload", "uploads", "user", "users", "var"
        ]
    }
    
    for filename, words in wordlists.items():
        wordlist_path = wordlists_dir / filename
        with open(wordlist_path, 'w') as f:
            f.write('\n'.join(words))
        print(f"✅ Wordlist criada: {filename}")

def test_installation():
    """Testa a instalação"""
    print("\n🧪 Testando instalação...")

    try:
        # Testa imports Python
        import requests
        import json
        from pathlib import Path
        print("✅ Imports Python - OK")

        # Testa se os arquivos principais existem
        main_files = [
            "guardian_main.py",
            "src/core/guardian.py",
            "src/core/config.py",
            "src/__init__.py"
        ]
        for file in main_files:
            if Path(file).exists():
                print(f"✅ {file} - Encontrado")
            else:
                print(f"❌ {file} - Não encontrado")
                return False

        # Testa import do módulo principal
        import sys
        sys.path.insert(0, str(Path(__file__).parent / "src"))

        try:
            from src.core.guardian import GuardianIA
            print("✅ Import Guardian IA - OK")
        except ImportError as e:
            print(f"❌ Erro no import Guardian IA: {e}")
            return False

        print("✅ Instalação testada com sucesso!")
        return True

    except ImportError as e:
        print(f"❌ Erro de import: {e}")
        return False

def main():
    """Função principal do setup"""
    print_banner()
    
    # Verifica Python
    if not check_python_version():
        sys.exit(1)
    
    # Instala dependências Python
    if not install_python_dependencies():
        print("\n⚠️  Algumas dependências Python falharam. Tente instalar manualmente:")
        print("pip install requests beautifulsoup4 urllib3 jinja2")
        # Não sai aqui, continua com o resto
    
    # Verifica ferramentas Go
    missing_tools = check_go_tools()
    if missing_tools:
        print(f"\n⚠️  {len(missing_tools)} ferramentas Go não encontradas.")
        install_choice = input("Deseja tentar instalar automaticamente? (y/n): ").lower()
        if install_choice == 'y':
            install_go_tools(missing_tools)
        else:
            print("\n📋 Para instalar manualmente:")
            for tool, install_path in missing_tools:
                print(f"go install -v {install_path}")
    
    # Cria diretórios
    create_directories()
    
    # Baixa wordlists
    download_wordlists()
    
    # Testa instalação
    test_installation()
    
    print("\n" + "="*80)
    print("🎉 SETUP CONCLUÍDO!")
    print("="*80)
    print("Para executar o Guardian IA:")
    print("python guardian_main.py --target exemplo.com")
    print("\nPara executar fases específicas:")
    print("python guardian_main.py --target exemplo.com --phases 1,2,3")
    print("\nPara modo seguro:")
    print("python guardian_main.py --target exemplo.com --safe-mode")
    print("="*80)

if __name__ == "__main__":
    main()
