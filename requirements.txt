# Guardian IA - Dependências Python
# Instalar com: pip install -r requirements.txt

# Requisições HTTP
requests>=2.28.0

# Parsing HTML/XML
beautifulsoup4>=4.11.0

# Manipulação de URLs
urllib3>=1.26.0

# Processamento JSON avançado
jsonschema>=4.0.0

# Templates HTML (opcional para relatórios)
jinja2>=3.1.0

# Gráficos e visualização (opcional)
matplotlib>=3.5.0
seaborn>=0.11.0

# Processamento de dados
pandas>=1.4.0

# Colorização de terminal
colorama>=0.4.0

# Validação de dados
pydantic>=1.9.0

# Logging avançado
loguru>=0.6.0

# Análise de URLs
tldextract>=3.4.0

# Processamento de CSV
csv-parser>=1.0.0

# Utilitários de sistema
psutil>=5.9.0

# Criptografia (para hashing)
cryptography>=3.4.0

# Expressões regulares avançadas
regex>=2022.0.0
