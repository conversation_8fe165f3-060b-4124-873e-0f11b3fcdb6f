# src/phases/__init__.py - <PERSON><PERSON><PERSON><PERSON> das 6 fases do Guardian IA

"""
<PERSON><PERSON><PERSON><PERSON> das 6 fases do Guardian IA

Este módulo contém todas as implementações das 6 fases da metodologia Guardian IA:

1. Fase 1 - Reconhecimento Estratégico e Coleta de Inteligência
2. Fase 2 - Enumeração Ativa e Mapeamento da Superfície de Ataque
3. Fase 3 - Análise de Vulnerabilidades e Testes de Intrusão
4. Fase 4 - Exploração e Análise de Impacto
5. Fase 5 - Pós-Exploração e Análise de Movimento Lateral
6. Fase 6 - Relatório Inteligente e Plano de Ação

Cada fase é implementada como uma classe independente que pode ser usada
individualmente ou orquestrada pela classe principal GuardianIA.
"""

# Imports das classes principais de cada fase
try:
    from .fase1_recon import ReconhecimentoEstrategico
    from .fase2_enum import EnumeracaoAtiva
    from .fase3_vulnerabilidades import AnalisadorVulnerabilidades
    from .fase4_exploracao import ExploracaoImpacto
    from .fase5_pos_exploracao import PosExploracaoAnalise
    from .fase6_relatorio import RelatorioInteligente

    # Lista de todas as classes de fase disponíveis
    __all__ = [
        'ReconhecimentoEstrategico',
        'EnumeracaoAtiva',
        'AnalisadorVulnerabilidades',
        'ExploracaoImpacto',
        'PosExploracaoAnalise',
        'RelatorioInteligente'
    ]

except ImportError as e:
    # Se alguma fase não puder ser importada, registra o erro
    import warnings
    warnings.warn(f"Erro ao importar fases: {e}", ImportWarning)
    __all__ = []

# Metadados do módulo
__version__ = "1.0.0"
__author__ = "Guardian IA Team"
__description__ = "Implementação das 6 fases da metodologia Guardian IA"
