# src/phases/fase1_recon.py - Fase 1: Reconhecimento Estratégico e Coleta de Inteligência

import os
import json
import subprocess
import requests
import re
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Optional, Tuple
from dataclasses import dataclass, asdict

# Imports da nova estrutura
from ..core.logger import get_logger
from ..core.config import DEFAULT_RECON_CONFIG
from ..utils.file_utils import save_json, load_json, save_csv
from ..utils.command_utils import CommandExecutor, ToolRunner

@dataclass
class ReconConfig:
    """Configurações para o reconhecimento"""
    timeout: int = 30
    threads: int = 50
    wordlists: List[str] = None
    use_passive_only: bool = False
    include_cloud_enum: bool = True
    deep_scan: bool = False
    
    def __post_init__(self):
        if self.wordlists is None:
            self.wordlists = [
                "/usr/share/wordlists/SecLists/Discovery/DNS/subdomains-top1million-5000.txt",
                "/usr/share/wordlists/SecLists/Discovery/DNS/fierce-hostlist.txt"
            ]

@dataclass
class SubdomainInfo:
    """Informações detalhadas de um subdomínio"""
    domain: str
    status_code: Optional[int] = None
    title: Optional[str] = None
    technologies: List[str] = None
    content_length: Optional[int] = None
    response_time: Optional[float] = None
    ip_address: Optional[str] = None
    cdn: Optional[str] = None
    server: Optional[str] = None
    screenshot_path: Optional[str] = None
    
    def __post_init__(self):
        if self.technologies is None:
            self.technologies = []

class ReconhecimentoEstrategico:
    """Classe principal para reconhecimento estratégico"""
    
    def __init__(self, alvo: str, diretorio_saida: str, config: ReconConfig = None):
        self.alvo = alvo
        self.diretorio_saida = Path(diretorio_saida)
        self.config = config or ReconConfig()
        self.resultados = {}
        self._criar_estrutura_diretorios()
        self.logger = self._configurar_logging()
    def _configurar_logging(self):
        """Configura logging detalhado para arquivo e console"""
        self.logger = get_logger(f"recon_{self.alvo}")
        return self.logger
    
    def _criar_estrutura_diretorios(self):
        """Cria estrutura organizada de diretórios"""
        subdirs = ["subdominios", "screenshots", "vulnerabilidades", "tecnologias", "ips", "relatorios"]
        for subdir in subdirs:
            (self.diretorio_saida / subdir).mkdir(exist_ok=True, parents=True)
    
    def _executar_ferramenta_segura(self, comando: List[str], arquivo_saida: str) -> bool:
        """Executa comando com tratamento de erro aprimorado"""
        try:
            self.logger.info(f"Executando: {' '.join(comando)}")
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                processo = subprocess.run(
                    comando, stdout=f, stderr=subprocess.PIPE,
                    timeout=self.config.timeout * 60, text=True, encoding='utf-8'
                )
            if processo.returncode == 0:
                self.logger.info(f"Sucesso ao executar {comando[0]}.")
                return True
            else:
                if processo.stderr: self.logger.error(f"Erro na execução de {comando[0]}: {processo.stderr.strip()}")
                return False
        except FileNotFoundError:
            self.logger.error(f"Ferramenta {comando[0]} não encontrada. Verifique se está instalada e no PATH.")
            return False
        except subprocess.TimeoutExpired:
            self.logger.error(f"Timeout na execução de {comando[0]} após {self.config.timeout} minutos.")
            return False
        except Exception as e:
            self.logger.error(f"Erro inesperado em {comando[0]}: {str(e)}")
            return False
    
    def _contar_linhas_arquivo(self, arquivo: str) -> int:
        """Conta linhas não vazias em um arquivo"""
        try:
            with open(arquivo, 'r', errors='ignore') as f:
                return sum(1 for linha in f if linha.strip())
        except:
            return 0
            
    def enumeracao_subdominios_passiva(self) -> Set[str]:
        # (código da sua função)
        self.logger.info("🔍 Iniciando enumeração passiva de subdomínios...")
        ferramentas = {
            "subfinder": ["subfinder", "-d", self.alvo, "-silent"],
            "amass": ["amass", "enum", "-passive", "-d", self.alvo],
            "assetfinder": ["assetfinder", "--subs-only", self.alvo],
            "findomain": ["findomain", "-t", self.alvo, "-q"],
        }
        todos_subdominios = set()
        for nome, comando in ferramentas.items():
            arquivo_saida = self.diretorio_saida / "subdominios" / f"{nome}.txt"
            if self._executar_ferramenta_segura(comando, str(arquivo_saida)):
                subs = self._ler_subdominios_arquivo(str(arquivo_saida))
                todos_subdominios.update(subs)
                self.logger.info(f"  {nome}: {len(subs)} subdomínios encontrados.")
        
        subs_crt = self._extrair_crt_sh()
        if subs_crt:
            todos_subdominios.update(subs_crt)
            self.logger.info(f"  crt.sh: {len(subs_crt)} subdomínios encontrados.")

        self.resultados['enumeracao_passiva'] = {'total_subdominios': len(todos_subdominios)}
        self.logger.info(f"✅ Enumeração passiva concluída: {len(todos_subdominios)} subdomínios únicos")
        return todos_subdominios
    
    def _extrair_crt_sh(self) -> Set[str]:
        # (código da sua função)
        subdominios = set()
        try:
            response = requests.get(f"https://crt.sh/?q=%.{self.alvo}&output=json", timeout=30)
            if response.status_code == 200:
                for cert in response.json():
                    for domain in cert.get('name_value', '').split('\n'):
                        domain = domain.strip().lower()
                        if domain and self.alvo in domain:
                            subdominios.add(re.sub(r'^\*\.', '', domain))
        except Exception as e:
            self.logger.error(f"Erro ao consultar crt.sh: {str(e)}")
        return subdominios

    def enumeracao_subdominios_ativa(self, subdominios_conhecidos: Set[str]) -> Set[str]:
        # (código da sua função)
        if self.config.use_passive_only: return set()
        self.logger.info("🔨 Iniciando enumeração ativa de subdomínios...")
        novos_subdominios = set()
        for wordlist in self.config.wordlists:
            if not os.path.exists(wordlist):
                self.logger.warning(f"Wordlist não encontrada: {wordlist}")
                continue
            arquivo_saida = self.diretorio_saida / "subdominios" / f"ativos_{Path(wordlist).stem}.txt"
            comando = ["gobuster", "dns", "-d", self.alvo, "-w", wordlist, "-t", str(self.config.threads), "--timeout", f"{self.config.timeout}s", "-q"]
            if self._executar_ferramenta_segura(comando, str(arquivo_saida)):
                subs_ativos = self._ler_subdominios_arquivo(str(arquivo_saida), extrair_gobuster=True)
                novos_subdominios.update(subs_ativos)
                self.logger.info(f"  Wordlist {Path(wordlist).name}: {len(subs_ativos)} novos subdomínios")
        self.logger.info(f"✅ Enumeração ativa concluída: {len(novos_subdominios)} novos subdomínios")
        return novos_subdominios

    def validacao_e_enriquecimento(self, subdominios: Set[str]) -> List[SubdomainInfo]:
        # (código da sua função)
        self.logger.info(f"🌐 Validando e enriquecendo {len(subdominios)} subdomínios...")
        arquivo_lista = self.diretorio_saida / "subdominios" / "para_validacao.txt"
        self._salvar_subdominios(subdominios, str(arquivo_lista))
        
        arquivo_httpx = self.diretorio_saida / "subdominios" / "ativos_detalhados.json"
        comando = ["httpx", "-l", str(arquivo_lista), "-status-code", "-title", "-tech-detect", "-server", "-content-length", "-response-time", "-location", "-cdn", "-ip", "-threads", str(self.config.threads), "-timeout", str(self.config.timeout), "-json", "-silent"]
        
        subdominios_ativos_info = []
        if self._executar_ferramenta_segura(comando, str(arquivo_httpx)):
            subdominios_ativos_info = self._processar_resultados_httpx(str(arquivo_httpx))
        
        if subdominios_ativos_info and not self.config.use_passive_only:
            self._capturar_screenshots(subdominios_ativos_info)
            
        self.resultados['validacao'] = {'total_testados': len(subdominios), 'ativos_encontrados': len(subdominios_ativos_info)}
        self.logger.info(f"✅ Validação concluída: {len(subdominios_ativos_info)} subdomínios ativos")
        return subdominios_ativos_info

    def _processar_resultados_httpx(self, arquivo_json: str) -> List[SubdomainInfo]:
        # (código da sua função)
        subdominios_info = []
        try:
            with open(arquivo_json, 'r') as f:
                for linha in f:
                    if linha.strip():
                        data = json.loads(linha)
                        info = SubdomainInfo(domain=data.get('url', ''), status_code=data.get('status_code'), title=data.get('title', '').strip()[:100], technologies=data.get('tech', []), content_length=data.get('content_length'), response_time=data.get('response_time'), ip_address=data.get('host'), cdn=data.get('cdn'), server=data.get('server'))
                        subdominios_info.append(info)
        except Exception as e:
            self.logger.error(f"Erro ao processar resultados HTTPX: {str(e)}")
        return subdominios_info

    def _capturar_screenshots(self, subdominios_info: List[SubdomainInfo]):
        # (código da sua função)
        self.logger.info("📸 Capturando screenshots dos principais alvos...")
        urls_interessantes = [info.domain for info in subdominios_info if info.status_code and 200 <= info.status_code < 400]
        if not urls_interessantes: return
        
        arquivo_urls = self.diretorio_saida / "screenshots" / "urls.txt"
        with open(arquivo_urls, 'w') as f:
            for url in urls_interessantes[:10]:
                f.write(f"{url}\n")
        
        comando = ["gowitness", "file", "-f", str(arquivo_urls), "-P", str(self.diretorio_saida / "screenshots"), "--threads", "5"]
        self._executar_ferramenta_segura(comando, str(self.diretorio_saida / "screenshots" / "log.txt"))

    def analise_tecnologias(self, subdominios_info: List[SubdomainInfo]) -> Dict:
        # (código da sua função)
        self.logger.info("🔧 Analisando tecnologias identificadas...")
        contadores_tech = {}; servidores = {}; cdns = {}
        for info in subdominios_info:
            for tech in info.technologies: contadores_tech[tech] = contadores_tech.get(tech, 0) + 1
            if info.server: servidores[info.server] = servidores.get(info.server, 0) + 1
            if info.cdn: cdns[info.cdn] = cdns.get(info.cdn, 0) + 1
        analise = {'tecnologias_mais_comuns': sorted(contadores_tech.items(), key=lambda x: x[1], reverse=True)[:10], 'servidores_mais_comuns': sorted(servidores.items(), key=lambda x: x[1], reverse=True)[:5], 'cdns_identificados': sorted(cdns.items(), key=lambda x: x[1], reverse=True)[:5]}
        with open(self.diretorio_saida / "tecnologias" / "analise.json", 'w') as f:
            json.dump(analise, f, indent=2, ensure_ascii=False)
        return analise

    def gerar_relatorio_fase1(self, subdominios_info: List[SubdomainInfo], analise_tech: Dict):
        # (código da sua função)
        self.logger.info("📋 Gerando relatório da Fase 1...")
        # (a lógica de geração de relatório continua aqui)

    def _gerar_relatorio_texto(self, relatorio: Dict, timestamp: str):
        # (código da sua função)
        pass # Implementar a geração de texto detalhado
    
    def _ler_subdominios_arquivo(self, arquivo: str, extrair_gobuster: bool = False) -> Set[str]:
        # (código da sua função)
        subdominios = set()
        try:
            with open(arquivo, 'r', errors='ignore') as f:
                for linha in f:
                    subdominio = linha.strip().lower()
                    if extrair_gobuster:
                        match = re.search(r'Found:\s*(.*)', subdominio, re.IGNORECASE)
                        if match: subdominio = match.group(1).split(' ')[0].strip()
                    if subdominio and self.alvo in subdominio and not subdominio.startswith('*'):
                        subdominios.add(subdominio)
        except Exception as e:
            self.logger.error(f"Erro ao ler arquivo {arquivo}: {str(e)}")
        return subdominios

    def _salvar_subdominios(self, subdominios: Set[str], arquivo: str):
        # (código da sua função)
        with open(arquivo, 'w') as f:
            for subdominio in sorted(subdominios):
                f.write(f"{subdominio}\n")
    
    def executar_fase_completa(self) -> Dict:
        """Executa o fluxo completo da Fase 1"""
        inicio = datetime.now()
        try:
            self.logger.info(f"🚀 INICIANDO FASE 1: RECONHECIMENTO ESTRATÉGICO PARA O ALVO: {self.alvo}")
            subdominios_passivos = self.enumeracao_subdominios_passiva()
            subdominios_ativos_bruteforce = self.enumeracao_subdominios_ativa(subdominios_passivos)
            todos_subdominios = subdominios_passivos.union(subdominios_ativos_bruteforce)
            subdominios_info = self.validacao_e_enriquecimento(todos_subdominios)
            analise_tech = self.analise_tecnologias(subdominios_info)
            self.gerar_relatorio_fase1(subdominios_info, analise_tech)
            
            duracao = datetime.now() - inicio
            self.logger.info("="*60)
            self.logger.info("🎉 FASE 1 CONCLUÍDA COM SUCESSO!")
            self.logger.info(f"⏱️  Duração total: {duracao}")
            return {'sucesso': True}
        except Exception as e:
            self.logger.error(f"❌ Erro crítico na Fase 1: {str(e)}", exc_info=True)
            return {'sucesso': False, 'erro': str(e)}

def run(alvo: str, diretorio_saida: str, config_dict: Dict = None):
    """Interface principal para executar a Fase 1 a partir de um dicionário de config"""
    config = ReconConfig(**config_dict) if config_dict else ReconConfig()
    reconhecimento = ReconhecimentoEstrategico(alvo, diretorio_saida, config)
    return reconhecimento.executar_fase_completa()
